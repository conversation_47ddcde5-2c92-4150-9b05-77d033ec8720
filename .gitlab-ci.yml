image: ${CI_DEPENDENCY_PROXY_GROUP_IMAGE_PREFIX}/amazoncorretto:11

default:
  tags:
    - friday
include:
  - template: Code-Quality.gitlab-ci.yml
  - template: Workflows/MergeRequest-Pipelines.gitlab-ci.yml
  - template: Security/SAST.gitlab-ci.yml

code_quality:
  rules:
    - if: '$CODE_QUALITY_DISABLED'
      when: never
    - if: '$CI_PIPELINE_SOURCE == "merge_request_event"' # Run code quality job in merge request pipelines
    - if: '$CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH'      # Run code quality job in pipelines on the master branch (but not in other branch pipelines)
    - if: '$CI_COMMIT_TAG'
      when: never

stages:
  - build
  - test
  - release
  - release_environments
  - deploy

sast:
  stage: test
  artifacts:
    paths: [ gl-sast-report.json, gl-code-quality-report.json ]
  when: manual
  allow_failure: true

variables:
  CONTAINER_RELEASE_IMAGE: $CI_REGISTRY_IMAGE:$CI_COMMIT_SHORT_SHA
  MAVEN_OPTS: "-Dhttps.protocols=TLSv1.2 -Dmaven.repo.local=$CI_PROJECT_DIR/.m2/repository -Dorg.slf4j.simpleLogger.log.org.apache.maven.cli.transfer.Slf4jMavenTransferListener=WARN -Dorg.slf4j.simpleLogger.showDateTime=true -Djava.awt.headless=true"
  MAVEN_CLI_OPTS: "--batch-mode --errors --fail-at-end --show-version -DinstallAtEnd=true -DdeployAtEnd=true"
  TASK_DEFINITION_NAME: "liveness-gateway-task"
  CLUSTER_NAME: "liveness-cluster"
  SERVICE_NAME: "liveness-service"

before_script:
  - chmod +x mvnw

build:
  stage: build
  allow_failure: false
  cache:
    paths:
      - .m2/repository
  script:
    - LOGGER_LEVELS_ROOT=OFF ./mvnw $MAVEN_CLI_OPTS jacoco:prepare-agent test jacoco:report verify -s ci/settings.xml
    - echo -n "Code coverage is "; grep -m1 -Po '(?<=<td class="ctr2">).*?(?=</td>)' target/site/jacoco/index.html | head -n1
    - mv target/liveness-gateway-0.1.jar target/liveness-gateway-$CI_COMMIT_SHORT_SHA.jar
    - export RELEASE_TO_MAVEN="./mvnw deploy:deploy-file -DgroupId=ai.friday -DartifactId=liveness-gateway -Dversion=$CI_COMMIT_SHORT_SHA -Dpackaging=jar -DrepositoryId=gitlab-maven -Dfile=target/liveness-gateway-$CI_COMMIT_SHORT_SHA.jar -DgeneratePom=true -Durl=https://gitlab.com/api/v4/projects/41867309/packages/maven --settings ci/settings.xml"
    - if [ "$CI_COMMIT_BRANCH" = "$CI_DEFAULT_BRANCH" ] || [ "$(echo "$CI_COMMIT_MESSAGE" | grep -E '#deploystg')" ]; then $RELEASE_TO_MAVEN; else echo "Sem release para o maven"; fi
  artifacts:
    paths:
      - target/site/jacoco
  except:
    - tags

release:
  image: ${CI_DEPENDENCY_PROXY_GROUP_IMAGE_PREFIX}/docker:stable
  allow_failure: false
  stage: release
  when: manual
  #  services:
  #    - docker:dind
  script:
    - apk add maven
    - apk add curl
    - ./mvnw dependency:copy -DremoteRepositories=https://gitlab.com/api/v4/projects/41867309/packages/maven -Dartifact=ai.friday:liveness-gateway:$CI_COMMIT_SHORT_SHA -DoutputDirectory=target/ --settings ci/settings.xml
    - docker login -u $CI_REGISTRY_USER -p $CI_REGISTRY_PASSWORD $CI_REGISTRY
    - wget -O dd-java-agent.jar 'https://dtdg.co/latest-java-tracer'
    - docker build --build-arg APP_VERSION=$CI_COMMIT_SHORT_SHA -t $CONTAINER_RELEASE_IMAGE .
    - docker push $CONTAINER_RELEASE_IMAGE

  rules:
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH || $CI_COMMIT_MESSAGE =~ /#deploystg/
      when: on_success

.release_environment: &release_environment
  - apk add --update --no-cache curl build-base py-pip python3-dev
  - python3 -V  # Print out python version for debugging.
  - pip install awscli --upgrade --user
  - export PATH=~/.local/bin:$PATH # Required for awscli.
  - aws configure set region $AWS_REGION
  - docker login -u $CI_REGISTRY_USER -p $CI_REGISTRY_PASSWORD $CI_REGISTRY
  - docker pull $CONTAINER_RELEASE_IMAGE
  - docker tag $CONTAINER_RELEASE_IMAGE $AWS_ECR_REPOSITORY:$CI_COMMIT_SHORT_SHA
  - $(aws ecr get-login --no-include-email)
  - docker push $AWS_ECR_REPOSITORY:$CI_COMMIT_SHORT_SHA

release_staging:
  image: ${CI_DEPENDENCY_PROXY_GROUP_IMAGE_PREFIX}/docker:stable
  allow_failure: true
  stage: release_environments
  environment:
    name: staging
  #  services:
  #    - docker:dind
  script:
    - *release_environment
  rules:
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH || $CI_COMMIT_MESSAGE =~ /#deploystg/
      when: on_success

release_production:
  image: ${CI_DEPENDENCY_PROXY_GROUP_IMAGE_PREFIX}/docker:stable
  stage: release_environments
  when: manual
  allow_failure: true
  environment:
    name: production
  #  services:
  #    - docker:dind
  script:
    - *release_environment
  only:
    - main

.deploy-ecs: &deploy-ecs
  - aws configure set region $AWS_REGION
  - TASK_DEFINITION=$(aws ecs describe-task-definition --task-definition "$TASK_DEFINITION_NAME")
  - NEW_TASK_DEFINITION=$(echo $TASK_DEFINITION | python $CI_PROJECT_DIR/ci/update_task_definition.py $AWS_ECR_REPOSITORY:$CI_COMMIT_SHORT_SHA)
  - aws ecs register-task-definition --cli-input-json "${NEW_TASK_DEFINITION}"
  - aws ecs update-service --cluster "${CLUSTER_NAME}" --service "${SERVICE_NAME}"  --task-definition "${TASK_DEFINITION_NAME}" --desired-count $DESIRED_COUNT

deploy_staging:
  stage: deploy
  image:
    name: ${CI_DEPENDENCY_PROXY_GROUP_IMAGE_PREFIX}/amazon/aws-cli:2.27.50
    entrypoint: [ "" ]
  environment:
    name: staging
  script:
    - export DESIRED_COUNT=1
    - *deploy-ecs
  rules:
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH || $CI_COMMIT_MESSAGE =~ /#deploystg/
      when: on_success

deploy_production:
  stage: deploy
  needs: [ release_production ]
  image:
    name: ${CI_DEPENDENCY_PROXY_GROUP_IMAGE_PREFIX}/amazon/aws-cli:2.27.50
    entrypoint: [ "" ]
  environment:
    name: production
  allow_failure: false
  script:
    - export DESIRED_COUNT=1
    - *deploy-ecs
  only:
    - main
