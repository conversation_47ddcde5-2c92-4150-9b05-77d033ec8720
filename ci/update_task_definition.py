import argparse
import json
import sys

parser = argparse.ArgumentParser('Replaces image in the task definition')
parser.add_argument('image_uri', metavar='I', type=str, nargs='+',
                   help='The new image URI')

args = parser.parse_args()

definition = json.load(sys.stdin)['taskDefinition']
definition['containerDefinitions'][0]['image'] = args.image_uri[0]

del definition['taskDefinitionArn']
del definition['revision']
del definition['status']
del definition['requiresAttributes']
del definition['compatibilities']
del definition['registeredAt']
del definition['registeredBy']

print json.dumps(definition)