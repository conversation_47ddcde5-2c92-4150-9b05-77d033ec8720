DEFAULT_GOAL = compile

MVN = ./mvnw

.PHONY: compile clean format-cc cc lint format

compile:
	$(MVN) test-compile

package:
	$(MVN) clean package -Dmaven.test.skip=true

clean:
	$(MVN) clean

format-cc: format cc

cc:
	$(MVN) clean test-compile

lint:
	$(MVN) antrun:run@ktlint

format:
	$(MVN) antrun:run@ktlint-format

pre-commit-install:
	echo "make format && git add ." > .git/hooks/pre-commit
	chmod +x .git/hooks/pre-commit

run-stg:
    $(MVN) mn:run -Dmicronaut.environments=picpay,stgpicpay