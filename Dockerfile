FROM public.ecr.aws/docker/library/debian:12
ARG APP_VERSION
ENV DD_VERSION $APP_VERSION
ENV SERVICE liveness-gateway
LABEL com.datadoghq.tags.version="$APP_VERSION"

COPY dd-java-agent.jar dd-java-agent.jar
COPY dd-java-agent.properties dd-java-agent.properties

RUN apt-get update && apt-get upgrade -y
RUN apt-get install -y default-jdk
RUN apt-get install -y maven
RUN apt-get install -y libgomp1
RUN mkdir /search3d3d

COPY target/${SERVICE}-*.jar ${SERVICE}.jar

EXPOSE 8443
CMD java -XX:+UseContainerSupport -javaagent:dd-java-agent.jar -Ddd.trace.config=dd-java-agent.properties -Ddd.profiling.enabled=true -Ddd.profiling.allocation.enabled=true -XX:FlightRecorderOptions=stackdepth=256 -Dcom.sun.management.jmxremote -noverify ${JAVA_OPTS} -jar ${SERVICE}.jar