# Campo Provider para Liveness

## Resumo das Mudanças

Foi adicionado um campo `provider` aos eventos de criação de liveness (enrollment e match) para identificar qual provedor de liveness está sendo usado. Atualmente, o único provedor suportado é "facetech", que é usado como valor padrão para manter compatibilidade com dados existentes.

## Estruturas Modificadas

### 1. Enum LivenessProvider
```kotlin
enum class LivenessProvider(val value: String) {
    FACETECH("facetech"),
}
```

### 2. Eventos de Liveness
```kotlin
data class EnrollmentCreated(
    override val livenessId: LivenessId,
    override val created: ZonedDateTime,
    val clientId: LivenessClientId,
    val externalId: ExternalId,
    val provider: LivenessProvider = LivenessProvider.FACETECH, // Novo campo
) : LivenessEvent()

data class MatchCreated(
    override val livenessId: LivenessId,
    override val created: ZonedDateTime,
    val clientId: LivenessClientId,
    val externalId: ExternalId,
    val provider: LivenessProvider = LivenessProvider.FACETECH, // Novo campo
) : LivenessEvent()
```

### 3. LivenessRequest
```kotlin
data class LivenessRequest(
    val externalId: ExternalId,
    val clientId: String,
    val provider: LivenessProvider = LivenessProvider.FACETECH, // Novo campo
)
```

### 4. CreateLivenessTO (DTO)
```kotlin
data class CreateLivenessTO(
    val externalId: String,
    val provider: String? = null, // Novo campo opcional
)
```

## Como Usar

### 1. Criando Enrollment com Provider Padrão (facetech)
```bash
curl -X POST http://localhost:8080/internal/enrollment \
  -H "Content-Type: application/json" \
  -H "Authorization: Basic <credentials>" \
  -d '{
    "externalId": "user123"
  }'
```

### 2. Criando Enrollment com Provider Explícito
```bash
curl -X POST http://localhost:8080/internal/enrollment \
  -H "Content-Type: application/json" \
  -H "Authorization: Basic <credentials>" \
  -d '{
    "externalId": "user123",
    "provider": "facetech"
  }'
```

### 3. Criando Match com Provider Padrão
```bash
curl -X POST http://localhost:8080/internal/match \
  -H "Content-Type: application/json" \
  -H "Authorization: Basic <credentials>" \
  -d '{
    "externalId": "user123"
  }'
```

### 4. Criando Match com Provider Explícito
```bash
curl -X POST http://localhost:8080/internal/match \
  -H "Content-Type: application/json" \
  -H "Authorization: Basic <credentials>" \
  -d '{
    "externalId": "user123",
    "provider": "facetech"
  }'
```

## Compatibilidade com Dados Existentes

- **Valor Padrão**: Todos os novos liveness criados sem especificar o provider usarão "facetech" como padrão
- **Dados Existentes**: Ao ler dados existentes do banco que não possuem o campo provider, o sistema automaticamente assume "facetech" como valor padrão
- **Retrocompatibilidade**: Clientes que não enviam o campo provider continuarão funcionando normalmente

## Armazenamento no DynamoDB

O campo provider é armazenado nas entidades do DynamoDB:

```kotlin
@JsonTypeName("EnrollmentCreated")
data class EnrollmentCreatedEntity(
    override val livenessId: String,
    override val created: ZonedDateTime,
    val clientId: String,
    val externalId: String,
    val provider: String = LivenessProvider.FACETECH.value, // Novo campo
) : LivenessEventEntity()

@JsonTypeName("MatchCreated")
data class MatchCreatedEntity(
    override val livenessId: String,
    override val created: ZonedDateTime,
    val clientId: String,
    val externalId: String,
    val provider: String = LivenessProvider.FACETECH.value, // Novo campo
) : LivenessEventEntity()
```

## Validação

- Se um provider inválido for enviado, o sistema usará "facetech" como fallback
- O campo provider é opcional na API - se não fornecido, usa "facetech" como padrão
- Valores aceitos atualmente: "facetech" (case-sensitive)

## Futuras Extensões

Para adicionar novos providers no futuro:

1. Adicionar nova entrada no enum `LivenessProvider`
2. Implementar adaptadores específicos para o novo provider
3. Atualizar validações se necessário

Exemplo:
```kotlin
enum class LivenessProvider(val value: String) {
    FACETECH("facetech"),
    NEW_PROVIDER("new_provider"), // Novo provider
}
```
