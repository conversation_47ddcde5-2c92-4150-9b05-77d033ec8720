# Rotação de chaves

O app consome as chaves do Liveness Gateway. Então, basicamente, basta alterar as chaves no secrets da AWS.

As chaves **duram 3 meses**, sendo a primeira a expirar em **2023-05-10**.

A FaceTec disponibiliza as novas chaves **1 mês antes da expiração** no dashboard (vide abaixo onde acessar).

## O que precisa ser alterado por causa da FaceTec

1) **PRODUCTION_KEY_TEXT**
    - Usado pelo servidor
    - Encontrado como um arquivo na aba “SDK Encryption Keys” do dashboard da FaceTec
    - Com 3/4 linhas e começa com appToken.


2) **CLIENT_PRODUCTION_KEY_TEXT**
    - Usado pelo app
    - Encontrado como um arquivo na aba “SDK Encryption Keys” do dashboard da FaceTec
    - Com 3/4 linhas e começa com appId.

## O que idealmente também precisaria ser alterado

Nossas chaves assimétricas para os face maps.

- A chave privada é a **FACE_MAP_ENCRYPTION_KEY**
- E a pública é **PUBLIC_FACE_SCAN_ENCRYPTION_KEY**.