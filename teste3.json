{"containerDefinitions": [{"name": "LivenessAPI", "image": "x", "cpu": 492, "memory": 1536, "portMappings": [{"containerPort": 8443, "hostPort": 8443, "protocol": "tcp"}], "essential": true, "environment": [{"name": "MICRONAUT_ENVIRONMENTS", "value": "staging"}, {"name": "DD_SERVICE", "value": "liveness-gateway"}, {"name": "DD_ENV", "value": "staging"}], "mountPoints": [], "volumesFrom": [], "secrets": [{"name": "INTEGRATIONS_FACETEC_SDK_DEVICE_KEY_IDENTIFIER", "valueFrom": "arn:aws:secretsmanager:us-east-1:402556871325:secret:liveness-gateway/facetec-UMJ3f8:DEVICE_KEY_IDENTIFIER::"}, {"name": "INTEGRATIONS_FACETEC_SDK_SERVER_KEY", "valueFrom": "arn:aws:secretsmanager:us-east-1:402556871325:secret:liveness-gateway/facetec-UMJ3f8:SERVER_KEY::"}, {"name": "INTEGRATIONS_FACETEC_SDK_PRODUCTION_KEY_TEXT", "valueFrom": "arn:aws:secretsmanager:us-east-1:402556871325:secret:liveness-gateway/facetec-UMJ3f8:PRODUCTION_KEY_TEXT::"}], "dockerLabels": {"com.datadoghq.tags.env": "staging", "com.datadoghq.tags.service": "liveness-gateway"}, "logConfiguration": {"logDriver": "awsfire<PERSON>s", "options": {"Name": "cloudwatch", "auto_create_group": "false", "log_group_name": "/ecs/liveness-gateway-task", "log_stream_name": "ecs/liveness-gateway-task/$(ecs_task_id)", "region": "us-east-1"}}}, {"name": "datadog-agent", "image": "datadog/agent:latest", "cpu": 10, "memory": 256, "portMappings": [{"containerPort": 8125, "hostPort": 8125, "protocol": "tcp"}], "essential": true, "environment": [{"name": "DD_SITE", "value": "datadoghq.com"}, {"name": "ECS_FARGATE", "value": "true"}], "mountPoints": [], "volumesFrom": [], "secrets": [{"name": "DD_API_KEY", "valueFrom": "arn:aws:secretsmanager:us-east-1:402556871325:secret:bill-payment-api/datadog-S3QmAq:DD_API_KEY::"}], "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "/ecs/liveness-gateway-dd", "awslogs-region": "us-east-1", "awslogs-stream-prefix": "ecs"}}}, {"name": "log_router", "image": "402556871325.dkr.ecr.us-east-1.amazonaws.com/custom-fluent-bit:2.3", "cpu": 0, "memoryReservation": 50, "portMappings": [], "essential": true, "environment": [{"name": "DD_SERVICE", "value": "liveness-gateway"}, {"name": "SERVICE_CONTAINER_NAME", "value": "LivenessAPI"}, {"name": "DD_ENV", "value": "staging"}, {"name": "LOG_GROUP_NAME", "value": "/logs/liveness-gateway-task"}], "mountPoints": [], "volumesFrom": [], "secrets": [{"name": "DD_API_KEY", "valueFrom": "arn:aws:secretsmanager:us-east-1:402556871325:secret:bill-payment-api/datadog-S3QmAq:DD_API_KEY::"}], "user": "0", "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "/ecs/liveness-gateway-dd", "awslogs-region": "us-east-1", "awslogs-stream-prefix": "ecs"}}, "firelensConfiguration": {"type": "fluentbit", "options": {"config-file-type": "file", "config-file-value": "/general/general.conf"}}}, {"name": "test-efs", "image": "402556871325.dkr.ecr.us-east-1.amazonaws.com/test-efs:1.0", "cpu": 10, "memory": 256, "portMappings": [{"containerPort": 3000, "hostPort": 3000, "protocol": "tcp"}, {"containerPort": 2049, "hostPort": 2049, "protocol": "tcp"}], "essential": false, "environment": [], "mountPoints": [{"sourceVolume": "test-efs-volume", "containerPath": "/logs"}], "volumesFrom": [], "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "/ecs/liveness-gateway-dd", "awslogs-region": "us-east-1", "awslogs-stream-prefix": "ecs"}}}], "family": "liveness-gateway-task", "taskRoleArn": "arn:aws:iam::402556871325:role/liveness-task-role", "executionRoleArn": "arn:aws:iam::402556871325:role/liveness-task-execution-role", "networkMode": "awsvpc", "volumes": [{"name": "test-efs-volume", "efsVolumeConfiguration": {"fileSystemId": "fs-01421857144706bc9", "rootDirectory": "/"}}], "placementConstraints": [], "requiresCompatibilities": ["FARGATE"], "cpu": "512", "memory": "2048"}