package ai.friday.liveness.app.liveness

import ai.friday.liveness.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.liveness.ByteWrapper
import ai.friday.liveness.app.LivenessClientId
import ai.friday.liveness.isUUID
import java.time.ZonedDateTime
import java.util.UUID
import kotlin.contracts.ExperimentalContracts
import kotlin.contracts.contract

// TODO: os construtores precisam ser privados.

sealed class Liveness {
    abstract val id: LivenessId
    abstract val externalId: ExternalId
    abstract val type: LivenessType
    abstract val createdAt: ZonedDateTime
    abstract val updatedAt: ZonedDateTime
    abstract val clientId: LivenessClientId

    open val status = LivenessStatus.CREATED

    open fun apply(event: EnrollmentCreated): Liveness {
        throw IllegalTransitionException(current = this, event = event)
    }

    open fun apply(event: MatchCreated): Liveness {
        throw IllegalTransitionException(current = this, event = event)
    }

    open fun apply(event: FaceScanReceived): Liveness {
        return this
    }

    open fun apply(event: FaceVerified): Liveness {
        throw IllegalTransitionException(current = this, event = event)
    }

    open fun apply(event: SessionCreated): Liveness {
        throw IllegalTransitionException(current = this, event = event)
    }

    open fun apply(event: DuplicationVerified): Liveness {
        throw IllegalTransitionException(current = this, event = event)
    }

    open fun apply(event: FaceMapUpdated): Liveness {
        throw IllegalTransitionException(current = this, event = event)
    }

    open fun apply(event: FraudVerified): Liveness {
        throw IllegalTransitionException(current = this, event = event)
    }
    open fun apply(event: MarkedAsFraud): Liveness {
        throw IllegalTransitionException(current = this, event = event)
    }

    fun apply(event: LivenessEvent): Liveness {
        return when (event) {
            is DuplicationVerified -> apply(event)
            is EnrollmentCreated -> apply(event)
            is MatchCreated -> apply(event)
            is FaceMapUpdated -> apply(event)
            is FaceVerified -> apply(event)
            is FaceScanReceived -> apply(event)
            is SessionCreated -> apply(event)
            is MarkedAsFraud -> apply(event)
            is FraudVerified -> apply(event)
        }
    }

    companion object {

        fun build(history: List<LivenessEvent>): Liveness {
            if (history.isEmpty()) {
                throw IllegalStateException("Cannot create liveness from empty history")
            }

            val creationEvent = history.first()
            val otherEvents = history.takeLast(history.size - 1)

            return otherEvents.fold(of(creationEvent)) { instance: Liveness, event ->
                instance.apply(event)
            }
        }

        fun build(vararg history: LivenessEvent): Liveness {
            return build(history.toList())
        }

        private fun of(event: LivenessEvent): PendingSessionCreation {
            return when (event) {
                is EnrollmentCreated -> PendingSessionCreation(event)
                is MatchCreated -> PendingSessionCreation(event)
                is DuplicationVerified, is FaceScanReceived, is FaceVerified, is FaceMapUpdated, is SessionCreated, is MarkedAsFraud, is FraudVerified -> throw IllegalStateException(
                    "First liveness event must be EnrollmentCreated or MatchCreated",
                )
            }
        }
    }
}

@ExperimentalContracts
fun isCompletedWithSuccess(liveness: Liveness?): Boolean {
    contract {
        returns(true) implies (liveness is CompletedLiveness)
    }
    return liveness is CompletedLiveness && liveness.faceMap != null && liveness.result == VerificationResult.Success
}

data class PendingSessionCreation(
    override val id: LivenessId,
    override val externalId: ExternalId,
    override val type: LivenessType,
    override val createdAt: ZonedDateTime,
    override val updatedAt: ZonedDateTime,
    override val clientId: LivenessClientId,
) : Liveness() {

    internal constructor(event: EnrollmentCreated) : this(
        id = event.livenessId,
        externalId = event.externalId,
        type = LivenessType.ENROLLMENT,
        createdAt = event.created,
        updatedAt = event.created,
        clientId = event.clientId,
    )

    internal constructor(event: MatchCreated) : this(
        id = event.livenessId,
        externalId = event.externalId,
        type = LivenessType.MATCH,
        createdAt = event.created,
        updatedAt = event.created,
        clientId = event.clientId,
    )

    override fun apply(event: SessionCreated): PendingFaceVerification {
        return PendingFaceVerification(
            id = this.id,
            externalId = this.externalId,
            type = this.type,
            createdAt = this.createdAt,
            updatedAt = event.created,
            deviceKeyIdentifier = event.deviceKeyIdentifier,
            userAgent = event.userAgent,
            clientIpAddress = event.clientIpAddress,
            attempt = 0,
            clientId = this.clientId,
        )
    }
}

data class PendingFaceVerification(
    override val id: LivenessId,
    override val externalId: ExternalId,
    override val type: LivenessType,
    override val createdAt: ZonedDateTime,
    override val updatedAt: ZonedDateTime,
    override val clientId: LivenessClientId,
    val deviceKeyIdentifier: String,
    val userAgent: String,
    val clientIpAddress: String,
    val attempt: Int,
) : Liveness() {
    override fun apply(event: FaceVerified): Liveness {
        val currentAttempt = this.attempt + 1
        return if (!event.success) {
            this.copy(
                updatedAt = event.created,
                attempt = currentAttempt,
            )
        } else {
            // TODO: implementar um limite de checagens, para o result poder se tornar "VerificationResult.Failure"
            CompletedLiveness(
                id = this.id,
                externalId = this.externalId,
                type = this.type,
                createdAt = this.createdAt,
                updatedAt = event.created,
                ageEstimateGroup = event.ageEstimateGroup,
                platformType = event.platformType,
                deviceSDKVersion = event.deviceSDKVersion,
                result = VerificationResult.Success,
                duplications = null,
                faceMap = event.faceMap?.let { ByteWrapper(it) },
                attempt = currentAttempt,
                deviceKeyIdentifier = this.deviceKeyIdentifier,
                userAgent = this.userAgent,
                clientIpAddress = this.clientIpAddress,
                fraudIndications = null,
                markedAsFraud = false,
                clientId = this.clientId,
            )
        }
    }

    override fun apply(event: SessionCreated): PendingFaceVerification {
        return copy(
            updatedAt = event.created,
            deviceKeyIdentifier = event.deviceKeyIdentifier,
            userAgent = event.userAgent,
            clientIpAddress = event.clientIpAddress,
        )
    }
}

sealed class VerificationResult {
    object Success : VerificationResult()
    data class Failure(
        val securityCheck: LivenessSecurityCheck,
    ) : VerificationResult()
}

data class CompletedLiveness(
    override val id: LivenessId,
    override val externalId: ExternalId,
    override val type: LivenessType,
    override val createdAt: ZonedDateTime,
    override val updatedAt: ZonedDateTime,
    override val clientId: LivenessClientId,
    val ageEstimateGroup: AgeEstimateGroup,
    val platformType: PlatformType,
    val deviceSDKVersion: String,
    val result: VerificationResult,
    val duplications: Set<ExternalId>?,
    val faceMap: ByteWrapper?,
    val attempt: Int,
    val deviceKeyIdentifier: String,
    val userAgent: String,
    val clientIpAddress: String,
    val fraudIndications: Set<ExternalId>?,
    val markedAsFraud: Boolean,
) : Liveness() {
    override val status = LivenessStatus.PROCESSED

    override fun apply(event: DuplicationVerified) = this.copy(
        duplications = addDuplications(this, event),
    )

    override fun apply(event: FaceMapUpdated) = this.copy(
        faceMap = ByteWrapper(event.updatedFaceMap),
    )

    override fun apply(event: FraudVerified) = this.copy(
        fraudIndications = addFrauds(this, event),
    )

    override fun apply(event: MarkedAsFraud) = this.copy(
        markedAsFraud = true,
    )
}

private fun addDuplications(completedLiveness: CompletedLiveness, event: DuplicationVerified): Set<ExternalId>? {
    if (completedLiveness.duplications == null && event.duplications != null) {
        return event.duplications
    }

    if (completedLiveness.duplications != null && event.duplications == null) {
        return completedLiveness.duplications
    }

    if (completedLiveness.duplications != null && event.duplications != null) {
        return completedLiveness.duplications.plus(event.duplications)
    }

    return null
}

private fun addFrauds(completedLiveness: CompletedLiveness, event: FraudVerified): Set<ExternalId>? {
    if (completedLiveness.fraudIndications == null && event.userList != null) {
        return event.userList
    }

    if (completedLiveness.fraudIndications != null && event.userList == null) {
        return completedLiveness.fraudIndications
    }

    if (completedLiveness.fraudIndications != null && event.userList != null) {
        return completedLiveness.fraudIndications.plus(event.userList)
    }

    return null
}

enum class LivenessStatus {
    CREATED, PROCESSED
}

enum class LivenessType {
    ENROLLMENT, MATCH
}

enum class LivenessProvider(val value: String) {
    FACETECH("facetech"),
}

data class ExternalId(val value: String) {
    init {
        require(value.trim().isNotBlank()) { "External id is blank" }
        require(value.length < 60)
    }
}

data class LivenessId(val value: String) {
    init {
        require(value.startsWith(prefix)) { "Should have prefix $prefix" }
        require(isUUID(value.removePrefix(prefix))) { "Should have uuid suffix" }
    }

    companion object {

        private const val prefix = "LIVENESS_ID-"
        fun generate(): LivenessId =
            LivenessId(prefix + UUID.randomUUID().toString())
    }
}

enum class LivenessEventType {
    DUPLICATION_VERIFIED, ENROLLMENT_CREATED, MATCH_CREATED, FACE_SCAN_RECEIVED, FACE_VERIFIED, SESSION_CREATED, FACE_MAP_UPDATED, MARKED_AS_FRAUD, FRAUD_VERIFIED
}

sealed class LivenessEvent {
    abstract val livenessId: LivenessId
    abstract val created: ZonedDateTime
    abstract val eventType: LivenessEventType
}

data class EnrollmentCreated(
    override val livenessId: LivenessId,
    override val created: ZonedDateTime,
    val clientId: LivenessClientId,
    val externalId: ExternalId,
    val provider: LivenessProvider,
) : LivenessEvent() {
    override val eventType = LivenessEventType.ENROLLMENT_CREATED
}

data class MatchCreated(
    override val livenessId: LivenessId,
    override val created: ZonedDateTime,
    val clientId: LivenessClientId,
    val externalId: ExternalId,
    val provider: LivenessProvider,
) : LivenessEvent() {
    override val eventType = LivenessEventType.MATCH_CREATED
}

data class FaceMapUpdated(
    override val livenessId: LivenessId,
    override val created: ZonedDateTime,
    val updatedFaceMap: String,
) : LivenessEvent() {
    override val eventType = LivenessEventType.FACE_MAP_UPDATED
}

data class SessionCreated(
    override val livenessId: LivenessId,
    override val created: ZonedDateTime,
    val deviceKeyIdentifier: String,
    val userAgent: String,
    val clientIpAddress: String,
) : LivenessEvent() {
    override val eventType = LivenessEventType.SESSION_CREATED
}

data class FaceScanReceived(
    override val livenessId: LivenessId,
    override val created: ZonedDateTime,
    val faceScanDigest: String,
) : LivenessEvent() {
    override val eventType = LivenessEventType.FACE_SCAN_RECEIVED
}

enum class AgeEstimateGroup {
    UNDER_THIRTEEN,
    OVER_THIRTEEN,
    OVER_EIGHTEEN,
    OVER_TWENTY_TWO,
    OVER_TWENTY_FIVE,
    OVER_THIRTY,
    UNKNOWN,
}

enum class PlatformType {
    ANDROID,
    IOS,
    BROWSER,
    UNKNOWN,
}

data class FaceVerified(
    override val livenessId: LivenessId,
    override val created: ZonedDateTime = getZonedDateTime(),
    val success: Boolean,
    val ageEstimateGroup: AgeEstimateGroup,
    val platformType: PlatformType,
    val deviceSDKVersion: String,
    val scanResultBlob: String,
    val securityCheck: LivenessSecurityCheck,
    val faceMap: String?,
    val auditTrailImage: String?,
) : LivenessEvent() {
    override val eventType = LivenessEventType.FACE_VERIFIED
}

data class DuplicationVerified(
    override val livenessId: LivenessId,
    override val created: ZonedDateTime = getZonedDateTime(),
    val duplications: Set<ExternalId>?,
) : LivenessEvent() {
    override val eventType = LivenessEventType.DUPLICATION_VERIFIED
}

data class MarkedAsFraud(
    override val livenessId: LivenessId,
    override val created: ZonedDateTime = getZonedDateTime(),
    val externalId: ExternalId,
) : LivenessEvent() {
    override val eventType = LivenessEventType.MARKED_AS_FRAUD
}

data class FraudVerified(
    override val livenessId: LivenessId,
    override val created: ZonedDateTime = getZonedDateTime(),
    val userList: Set<ExternalId>?,
) : LivenessEvent() {
    override val eventType = LivenessEventType.FRAUD_VERIFIED
}

data class IllegalTransitionException(
    val current: Liveness,
    val event: LivenessEvent,
    override val cause: Throwable? = null,
) :
    IllegalStateException("Cannot apply transition", cause)

data class LivenessRawImages(
    val faceMap: ByteArray,
    val auditTrailImage: ByteArray?,
)

data class DuplicationVerification(
    val duplications: Set<ExternalId>?,
)

data class EnrollmentVerification(
    val duplications: Set<ExternalId>?,
    val fraudIndications: Set<ExternalId>?,
)

data class MatchCompletedCheck(
    val livenessId: LivenessId,
    val externalId: ExternalId,
    val hasMatch: Boolean,
    val attempt: Int,
)