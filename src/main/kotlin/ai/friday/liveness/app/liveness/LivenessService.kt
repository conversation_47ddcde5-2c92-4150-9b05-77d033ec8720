package ai.friday.liveness.app.liveness

import ai.friday.liveness.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.liveness.ByteWrapper
import ai.friday.liveness.andAppend
import ai.friday.liveness.app.LivenessClientId
import ai.friday.liveness.app.auth.AuthConfig
import ai.friday.liveness.app.errors.GeneralError
import ai.friday.liveness.app.errors.LivenessError
import ai.friday.liveness.app.integrations.MessagePublisher
import ai.friday.liveness.app.integrations.MessagePublisherConfiguration
import ai.friday.liveness.app.integrations.enrollmentVerify
import ai.friday.liveness.app.session.LivenessRequest
import arrow.core.Either
import arrow.core.getOrHandle
import arrow.core.left
import arrow.core.right
import jakarta.inject.Named
import jakarta.inject.Singleton
import java.math.BigInteger
import java.security.MessageDigest
import kotlin.contracts.ExperimentalContracts
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

@OptIn(ExperimentalContracts::class)
@Singleton
open class LivenessService(
    private val livenessAdapter: LivenessAdapter,
    private val livenessEventRepository: LivenessEventRepository,
    private val featureConfiguration: FeatureConfiguration,
    private val messagePublisher: MessagePublisher,
    private val faceMapSearch: FaceMapSearchAdapter,
    @Named(enrollmentVerify) private val enrollmentVerifyDuplicationConfiguration: MessagePublisherConfiguration,
    private val authConfigurations: AuthConfig,
) {

    private val logger = LoggerFactory.getLogger(LivenessService::class.java)

    suspend fun createEnrollment(request: LivenessRequest): Either<SaveLivenessError, Liveness> {
        val logName = "LivenessService#createEnrollment"
        val markers = Markers.append("clientId", request.clientId)
            .andAppend("externalId", request.externalId.value)

        return Either.catch {
            val previousEnrollment = livenessEventRepository.findEnrollmentByExternalId(request.externalId)

            if (previousEnrollment != null) {
                return if (isCompletedWithSuccess(previousEnrollment)) {
                    SaveLivenessError.AlreadyEnrolled.left()
                } else {
                    previousEnrollment.right()
                }
            }

            val event = EnrollmentCreated(
                livenessId = LivenessId.generate(),
                created = getZonedDateTime(),
                clientId = LivenessClientId(request.clientId),
                externalId = request.externalId,
                provider = request.provider,
            )

            livenessEventRepository.save(event)
            logger.info(markers, logName)
            Liveness.build(event)
        }.mapLeft {
            logger.warn(markers, logName, it)
            return when (it) {
                is IllegalStateException, is IllegalArgumentException -> SaveLivenessError.InvalidExternalId
                else -> SaveLivenessError.Generic(it)
            }.left()
        }
    }

    suspend fun createFraud(externalId: ExternalId): Either<LivenessError, Liveness> {
        val enrollment = livenessEventRepository.findEnrollmentByExternalId(externalId)

        if (!isCompletedWithSuccess(enrollment)) {
            return LivenessError.InvalidLivenessState.left()
        }

        val liveness = livenessEventRepository.findByIdOrNull(enrollment.id) ?: return LivenessError.LivenessNotFound.left()

        val event = MarkedAsFraud(
            livenessId = liveness.id,
            created = getZonedDateTime(),
            externalId = liveness.externalId,
        )
        return try {
            faceMapSearch.saveFaceMap(externalId = liveness.externalId, faceMap = (liveness as CompletedLiveness).faceMap!!.bytes, group = FaceMatchGroup.FRAUD_USERS, clientId = liveness.clientId)
            livenessEventRepository.save(event)

            liveness.right()
        } catch (e: Exception) {
            GeneralError.ServerError(message = "Error creating fraud", cause = e).left()
        }
    }

    suspend fun hasCompletedEnrollment(request: LivenessRequest): Either<SaveLivenessError, Boolean> {
        return try {
            val previousEnrollment = livenessEventRepository.findEnrollmentByExternalId(request.externalId)
            (previousEnrollment != null && isCompletedWithSuccess(previousEnrollment)).right()
        } catch (e: Exception) {
            when (e) {
                is IllegalStateException, is IllegalArgumentException -> SaveLivenessError.InvalidExternalId
                else -> SaveLivenessError.Generic(e)
            }.left()
        }
    }

    suspend fun createMatch(request: LivenessRequest): Either<SaveLivenessError, Liveness> {
        return Either.catch {
            val enrollmentLiveness = livenessEventRepository.findEnrollmentByExternalId(request.externalId)
                ?: return SaveLivenessError.InvalidExternalId.left()

            if (!isCompletedWithSuccess(enrollmentLiveness)) {
                return SaveLivenessError.InvalidExternalId.left()
            }

            val event = MatchCreated(
                livenessId = LivenessId.generate(),
                created = getZonedDateTime(),
                clientId = LivenessClientId(request.clientId),
                externalId = request.externalId,
                provider = request.provider,
            )

            livenessEventRepository.save(event)
            Liveness.build(event)
        }.mapLeft {
            return when (it) {
                is IllegalStateException, is IllegalArgumentException -> SaveLivenessError.InvalidExternalId
                else -> SaveLivenessError.Generic(it)
            }.left()
        }
    }

    suspend fun check(request: LivenessCheckRequest): Either<LivenessError, LivenessCheckResult> {
        return withLiveness(request.livenessId) { liveness ->
            if (liveness !is PendingFaceVerification) {
                LivenessError.InvalidLivenessState.left()
            } else {
                when (liveness.type) {
                    LivenessType.ENROLLMENT -> enroll(liveness, request)
                    LivenessType.MATCH -> match(liveness, request)
                }
            }
        }
    }

    private suspend fun match(
        matchLiveness: Liveness,
        request: LivenessCheckRequest,
    ): Either<LivenessError, LivenessCheckResult> {
        try {
            val enrollmentLiveness = checkSecurityAgainstPreviousEnrollment(
                liveness = matchLiveness,
                request = request,
            ).map { previousEnrollmentLiveness ->
                if (!isCompletedWithSuccess(previousEnrollmentLiveness)) {
                    return LivenessError.InvalidLivenessState.left()
                }
                previousEnrollmentLiveness
            }.getOrHandle {
                return it.left()
            }

            if (enrollmentLiveness.faceMap == null) {
                return LivenessError.InvalidLivenessState.left()
            }

            val result = livenessAdapter.matchLiveness(request, enrollmentLiveness.faceMap).getOrHandle {
                return it.left()
            }

            storeLivenessResult(matchLiveness.id, request, result)

            val originalFaceMap = enrollmentLiveness.faceMap.getBase64()
            val resultFaceMap = if (result is LivenessCheckResult.Success) {
                result.faceMap.getBase64()
            } else {
                null
            }

            resultFaceMap?.let { updatedFaceMap ->
                if (updatedFaceMap != originalFaceMap) {
                    val matchCompletedEvent = FaceMapUpdated(
                        livenessId = enrollmentLiveness.id,
                        created = getZonedDateTime(),
                        updatedFaceMap = updatedFaceMap,
                    )
                    livenessEventRepository.save(matchCompletedEvent)
                }
            }

            return result.right()
        } catch (exception: Exception) {
            return GeneralError.ServerError(cause = exception).left()
        }
    }

    private suspend fun enroll(
        liveness: Liveness,
        request: LivenessCheckRequest,
    ): Either<LivenessError, LivenessCheckResult> {
        try {
            checkSecurityAgainstPreviousEnrollment(
                liveness = liveness,
                request = request,
            ).map { previousEnrollment ->
                if (isCompletedWithSuccess(previousEnrollment)) {
                    return LivenessError.DuplicatedExternalId.left()
                }
            }.getOrHandle {
                return it.left()
            }

            val result = livenessAdapter.checkLiveness(request).getOrHandle {
                return it.left()
            }

            val faceVerified = storeLivenessResult(liveness.id, request, result)

            if (featureConfiguration.oneToManySearch || featureConfiguration.isAllowedToFaceScan(liveness.externalId)) {
                if (faceVerified.success && faceVerified.faceMap != null) {
                    messagePublisher.sendMessage(
                        queue = enrollmentVerifyDuplicationConfiguration.queueName,
                        body = liveness.id,
                    )
                }
            }
            return result.right()
        } catch (exception: Exception) {
            return GeneralError.ServerError(cause = exception).left()
        }
    }

    private suspend fun checkSecurityAgainstPreviousEnrollment(
        liveness: Liveness,
        request: LivenessCheckRequest,
    ): Either<LivenessError, Liveness?> {
        val logName = "LivenessService#checkSecurityAgainstPreviousEnrollment"
        val markers = Markers.append("clientId", liveness.clientId.value)
            .andAppend("externalId", liveness.externalId.value)
            .andAppend("livenessId", liveness.id.value)

        val faceScanDigest = digestFaceScan(request.faceScan)
        val faceScanAlreadyReceived = livenessEventRepository.existsFaceScanDigest(faceScanDigest)

        val faceScanReceived = FaceScanReceived(
            livenessId = liveness.id,
            created = getZonedDateTime(),
            faceScanDigest = faceScanDigest,
        )

        livenessEventRepository.save(faceScanReceived)

        if (faceScanAlreadyReceived) {
            if (authConfigurations.tenantConfig(liveness.clientId).allowReplay) {
                logger.warn(markers, logName)
            } else {
                logger.error(markers, logName)
                return LivenessError.FaceScanReplayed.left()
            }
        }

        logger.info(markers, logName)
        return livenessEventRepository.findEnrollmentByExternalId(liveness.externalId).right()
    }

    private suspend fun storeLivenessResult(
        livenessId: LivenessId,
        request: LivenessCheckRequest,
        result: LivenessCheckResult,
    ): FaceVerified {
        val faceVerified = when (result) {
            is LivenessCheckResult.Failed -> FaceVerified(
                livenessId = livenessId,
                success = false,
                ageEstimateGroup = result.ageEstimateGroup,
                platformType = result.platformType,
                deviceSDKVersion = result.deviceSDKVersion,
                securityCheck = result.securityCheck,
                faceMap = null,
                auditTrailImage = request.auditTrailImage,
                scanResultBlob = result.scanResultBlob,
            )

            is LivenessCheckResult.Success -> FaceVerified(
                livenessId = livenessId,
                success = !result.retryRequired,
                ageEstimateGroup = result.ageEstimateGroup,
                platformType = result.platformType,
                deviceSDKVersion = result.deviceSDKVersion,
                securityCheck = result.securityCheck,
                faceMap = result.faceMap.getBase64(),
                auditTrailImage = request.auditTrailImage,
                scanResultBlob = result.scanResultBlob,
            )
        }

        livenessEventRepository.save(faceVerified)
        return faceVerified
    }

    suspend fun getDuplicationVerification(externalId: ExternalId): Either<LivenessError, DuplicationVerification> {
        val liveness = livenessEventRepository.findEnrollmentByExternalId(externalId)
            ?: return GeneralError.InvalidOperationId.left()

        if (!isCompletedWithSuccess(liveness)) {
            return LivenessError.InvalidLivenessState.left()
        }

        return DuplicationVerification(duplications = liveness.duplications).right()
    }

    suspend fun enrollmentVerification(externalId: ExternalId): Either<LivenessError, EnrollmentVerification> {
        val liveness = livenessEventRepository.findEnrollmentByExternalId(externalId)
            ?: return GeneralError.InvalidOperationId.left()

        if (!isCompletedWithSuccess(liveness)) {
            return LivenessError.InvalidLivenessState.left()
        }

        return EnrollmentVerification(
            duplications = liveness.duplications,
            fraudIndications = liveness.fraudIndications,
        ).right()
    }

    suspend fun retrieveAuditImage(livenessId: LivenessId): Either<LivenessError, ByteArray> {
        val liveness = livenessEventRepository.findByIdOrNull(livenessId) ?: return GeneralError.InvalidOperationId.left()
        if (!isCompletedWithSuccess(liveness)) {
            return LivenessError.InvalidLivenessState.left()
        }
        val rawImages =
            livenessEventRepository.findRawImagesData(livenessId) ?: return LivenessError.InvalidLivenessState.left()

        return if (rawImages.auditTrailImage != null) {
            rawImages.auditTrailImage.right()
        } else {
            livenessAdapter.extractAuditImageFromFacemap(rawImages.faceMap)
        }
    }

    suspend fun checkMatchCompleted(livenessId: LivenessId): Either<LivenessError, MatchCompletedCheck> {
        val checkLiveness = livenessEventRepository.findByIdOrNull(livenessId) ?: return GeneralError.InvalidOperationId.left()
        if (checkLiveness.type != LivenessType.MATCH) {
            return GeneralError.InvalidOperationId.left()
        }

        return when (checkLiveness) {
            is PendingSessionCreation -> MatchCompletedCheck(
                livenessId = livenessId,
                externalId = checkLiveness.externalId,
                hasMatch = false,
                attempt = 0,
            ).right()

            is PendingFaceVerification -> MatchCompletedCheck(
                livenessId = livenessId,
                externalId = checkLiveness.externalId,
                hasMatch = false,
                attempt = checkLiveness.attempt,
            ).right()

            is CompletedLiveness -> {
                MatchCompletedCheck(
                    livenessId = livenessId,
                    externalId = checkLiveness.externalId,
                    hasMatch = checkLiveness.result is VerificationResult.Success,
                    attempt = checkLiveness.attempt,
                ).right()
            }
        }
    }

    private fun digestFaceScan(faceScan: String): String {
        val messageDigest = MessageDigest.getInstance("SHA-256")
        val hashBytes = messageDigest.digest(ByteWrapper(faceScan).bytes)

        return BigInteger(1, hashBytes).toString(16)
    }

    private suspend fun <T> withLiveness(
        livenessId: LivenessId,
        toBeExecuted: suspend (liveness: Liveness) -> Either<LivenessError, T>,
    ): Either<LivenessError, T> {
        val liveness = livenessEventRepository.findByIdOrNull(livenessId) ?: return GeneralError.InvalidOperationId.left()

        if (liveness.status != LivenessStatus.CREATED) {
            return LivenessError.AlreadyProcessed.left()
        }

        return toBeExecuted(liveness)
    }
}

sealed class SaveLivenessError {
    data class Generic(val exception: Throwable) : SaveLivenessError()
    object InvalidExternalId : SaveLivenessError()
    object AlreadyEnrolled : SaveLivenessError()
}

enum class FaceMatchGroup { ENROLLED_USERS, FRAUD_USERS }

private fun AuthConfig.tenantEntry(clientId: LivenessClientId): Map.Entry<String, AuthConfig.TenantConfig> {
    val tenant = tenants.filterValues {
        clientId.value in listOf(it.main.username, it.backoffice.username)
    }

    if (tenant.size > 1) {
        throw IllegalStateException("Multiple tenants with same client id")
    }

    return tenant.entries.firstOrNull() ?: throw IllegalStateException("Tenant not found")
}

fun AuthConfig.tenantName(clientId: LivenessClientId) = tenantEntry(clientId).key
fun AuthConfig.tenantConfig(clientId: LivenessClientId) = tenantEntry(clientId).value
fun AuthConfig.tenantProvider(clientId: LivenessClientId): LivenessProvider {
    val providerValue = tenantConfig(clientId).provider
    return LivenessProvider.values().find { it.value == providerValue } ?: LivenessProvider.FACETECH
}