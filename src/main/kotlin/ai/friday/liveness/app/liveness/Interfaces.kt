package ai.friday.liveness.app.liveness

import ai.friday.liveness.ByteWrapper
import ai.friday.liveness.app.LivenessClientId
import ai.friday.liveness.app.errors.LivenessError
import arrow.core.Either

interface LivenessEventRepository {
    suspend fun save(event: LivenessEvent)
    suspend fun findByIdOrNull(livenessId: LivenessId): Liveness?
    suspend fun findEnrollmentByExternalId(externalId: ExternalId): Liveness?
    suspend fun existsFaceScanDigest(digest: String): Boolean
    suspend fun findRawImagesData(livenessId: LivenessId): LivenessRawImages?
    suspend fun findMatchByExternalId(externalId: ExternalId): List<Liveness>
}

interface LivenessAdapter {
    suspend fun checkLiveness(request: LivenessCheckRequest): Either<LivenessError, LivenessCheckResult>
    suspend fun matchLiveness(
        request: LivenessCheckRequest,
        faceMap: ByteWrapper,
    ): Either<LivenessError, LivenessCheckResult>

    suspend fun extractAuditImageFromFacemap(faceMap: ByteArray): Either<LivenessError, ByteArray>
}

interface FaceMapSearchAdapter {
    suspend fun saveFaceMap(externalId: ExternalId, faceMap: ByteArray, group: FaceMatchGroup = FaceMatchGroup.ENROLLED_USERS, clientId: LivenessClientId)
    suspend fun search3D(faceMap: ByteArray, group: FaceMatchGroup = FaceMatchGroup.ENROLLED_USERS, clientId: LivenessClientId): Either<Exception, List<ExternalId>>
}

interface FeatureConfiguration {
    val oneToManySearch: Boolean
    val allowedExternalIdPrefix: String
    fun isAllowedToFaceScan(externalId: ExternalId): Boolean {
        return externalId.value.startsWith(allowedExternalIdPrefix)
    }
}