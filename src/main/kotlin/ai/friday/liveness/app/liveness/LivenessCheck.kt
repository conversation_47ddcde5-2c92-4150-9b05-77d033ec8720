package ai.friday.liveness.app.liveness

import ai.friday.liveness.ByteWrapper

data class LivenessCheckRequest(
    val faceScan: String,
    val auditTrailImage: String,
    val lowQualityAuditTrailImage: String?,
    val deviceKeyIdentifier: String,
    val livenessId: LivenessId,
    val userAgent: String,
    val clientIpAddress: String,
)

// TODO: fazer construtores serem privados
sealed class LivenessCheckResult {
    data class Success(
        val retryRequired: Boolean,
        val ageEstimateGroup: AgeEstimateGroup,
        val platformType: PlatformType,
        val deviceSDKVersion: String,
        val scanResultBlob: String,
        val securityCheck: LivenessSecurityCheck,
        val faceMap: ByteWrapper,
    ) : LivenessCheckResult()

    data class Failed(
        val ageEstimateGroup: AgeEstimateGroup,
        val platformType: PlatformType,
        val deviceSDKVersion: String,
        val scanResultBlob: String,
        val securityCheck: LivenessSecurityCheck,
    ) : LivenessCheckResult()

    companion object {
        fun createSuccessResult(
            faceMap: ByteWrapper,
            ageEstimateGroup: AgeEstimateGroup,
            platformType: PlatformType,
            deviceSDKVersion: String,
            securityCheck: LivenessSecurityCheck,
            scanResultBlobFactory: (retryRequired: Boolean) -> String,
        ): Success {
            val retryRequired =
                !securityCheck.faceScanLivenessCheck || !securityCheck.auditTrailVerificationCheck || !securityCheck.sessionTokenCheck

            return Success(
                faceMap = faceMap,
                ageEstimateGroup = ageEstimateGroup,
                platformType = platformType,
                deviceSDKVersion = deviceSDKVersion,
                securityCheck = securityCheck,
                scanResultBlob = scanResultBlobFactory(retryRequired),
                retryRequired = retryRequired,
            )
        }

        fun createFailureResult(
            ageEstimateGroup: AgeEstimateGroup,
            platformType: PlatformType,
            deviceSDKVersion: String,
            securityCheck: LivenessSecurityCheck,
            scanResultBlobFactory: (retryRequired: Boolean) -> String,
        ): Failed {
            return Failed(
                ageEstimateGroup = ageEstimateGroup,
                platformType = platformType,
                deviceSDKVersion = deviceSDKVersion,
                securityCheck = securityCheck,
                scanResultBlob = scanResultBlobFactory(true),
            )
        }
    }
}

data class LivenessSecurityCheck(
    val faceScanLivenessCheck: Boolean,
    val auditTrailVerificationCheck: Boolean,
    val sessionTokenCheck: Boolean,
)