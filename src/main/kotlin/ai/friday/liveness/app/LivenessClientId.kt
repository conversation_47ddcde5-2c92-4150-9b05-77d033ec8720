package ai.friday.liveness.app

import ai.friday.liveness.isUUID
import java.util.UUID

data class LivenessClientId(val value: String) {
    init {
        require(value.isNotBlank()) { "Liveness client id is blank" }
        require(isUUID(value.removePrefix(PREFIX))) { "Should have uuid suffix" }
    }

    companion object {
        private const val PREFIX = "LIVENESS_CLIENT_ID-"
        fun generate(): LivenessClientId =
            LivenessClientId(PREFIX + UUID.randomUUID().toString())
    }
}