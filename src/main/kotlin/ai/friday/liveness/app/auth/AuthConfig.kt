package ai.friday.liveness.app.auth

import io.micronaut.context.annotation.ConfigurationProperties

@ConfigurationProperties("auth")
class AuthConfig {

    lateinit var tenants: Map<String, TenantConfig>

    class TenantConfig {
        var allowReplay: Boolean = false
        lateinit var main: Credentials
        lateinit var backoffice: Credentials
    }

    class Credentials {
        lateinit var username: String
        lateinit var password: String
    }
}