package ai.friday.liveness.app.session

import ai.friday.liveness.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.liveness.app.documentscan.DocumentScanEventRepository
import ai.friday.liveness.app.documentscan.DocumentScanId
import ai.friday.liveness.app.documentscan.DocumentScanSessionCreated
import ai.friday.liveness.app.documentscan.PendingSessionCreation
import ai.friday.liveness.app.errors.SessionError
import ai.friday.liveness.app.liveness.LivenessEventRepository
import ai.friday.liveness.app.liveness.LivenessId
import ai.friday.liveness.app.liveness.LivenessStatus
import ai.friday.liveness.app.liveness.SessionCreated
import arrow.core.Either
import arrow.core.left
import jakarta.inject.Singleton

@Singleton
open class SessionService(
    private val sessionAdapter: SessionAdapter,
    private val livenessEventRepository: LivenessEventRepository,
    private val documentScanEventRepository: DocumentScanEventRepository,
) {

    suspend fun createSession(request: SessionTokenRequest) = when (request.type) {
        OperationType.LIVENESS_CHECK -> processLivenessCreateSession(request)
        OperationType.DOCUMENT_SCAN -> processScanCreateSession(request)
    }

    private suspend fun processScanCreateSession(result: SessionTokenRequest): Either<SessionError, SessionTokenResult> {
        val scan = documentScanEventRepository.findByIdOrNull(DocumentScanId(result.operationId)) ?: return SessionError.NotProcessable.left()

        if (scan !is PendingSessionCreation) {
            return SessionError.NotProcessable.left()
        }

        val sessionResult = sessionAdapter.createSession(result)
        val event = DocumentScanSessionCreated(
            id = DocumentScanId(result.operationId),
            created = getZonedDateTime(),
            deviceKeyIdentifier = result.deviceKeyIdentifier,
            userAgent = result.userAgent,
            clientIpAddress = result.clientIpAddress,
        )
        documentScanEventRepository.save(event)
        return sessionResult
    }

    private suspend fun processLivenessCreateSession(request: SessionTokenRequest): Either<SessionError, SessionTokenResult> {
        val liveness = livenessEventRepository.findByIdOrNull(LivenessId(request.operationId)) ?: return SessionError.NotProcessable.left()

        if (liveness.status != LivenessStatus.CREATED) {
            return SessionError.NotProcessable.left()
        }

        val sessionResult = sessionAdapter.createSession(request)
        val event = SessionCreated(
            livenessId = LivenessId(request.operationId),
            created = getZonedDateTime(),
            deviceKeyIdentifier = request.deviceKeyIdentifier,
            userAgent = request.userAgent,
            clientIpAddress = request.clientIpAddress,
        )

        livenessEventRepository.save(event)
        return sessionResult
    }
}