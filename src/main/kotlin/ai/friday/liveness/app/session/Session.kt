package ai.friday.liveness.app.session

import ai.friday.liveness.app.liveness.ExternalId
import com.fasterxml.jackson.annotation.JsonValue

data class SessionTokenRequest(
    val operationId: String,
    val type: OperationType,
    val deviceKeyIdentifier: String,
    val userAgent: String,
    val clientIpAddress: String,
)

data class OperationId(val value: String) // TODO verificacoes

enum class OperationType(@JsonValue val value: String) {
    LIVENESS_CHECK("LivenessCheck"),
    DOCUMENT_SCAN("DocumentScan"),
}

data class SessionTokenResult(
    val token: String,
)

data class LivenessRequest(
    val externalId: ExternalId,
    val clientId: String,
)