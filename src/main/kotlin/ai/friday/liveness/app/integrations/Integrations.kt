package ai.friday.liveness.app.integrations

import io.micronaut.context.annotation.ConfigurationInject
import io.micronaut.context.annotation.EachProperty
import io.micronaut.context.annotation.Parameter
import io.micronaut.context.annotation.Property
import io.micronaut.core.bind.annotation.Bindable

const val enrollmentVerify = "enrollment-verify-duplication"

interface MessagePublisher {
    fun sendMessage(queue: String, body: Any, delay: Int? = null)
}

@EachProperty("aws.sqs.publisher")
class MessagePublisherConfiguration
@ConfigurationInject constructor(
    @param:Parameter val name: String,
    val queueName: String,
    @Property(name = "delay", defaultValue = "0") val delay: Int,
)

@EachProperty("aws.sqs.handler")
class MessageHandlerConfiguration
@ConfigurationInject constructor(
    @param:Parameter val name: String,
    val queueName: String,
    val timeWindowCron: String? = null,
    @Bindable(defaultValue = "300") val timeWindowToleranceInSeconds: Long,
    @Bindable(defaultValue = "1")
    val consumers: Int,
)