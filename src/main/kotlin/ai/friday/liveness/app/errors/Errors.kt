package ai.friday.liveness.app.errors

import com.fasterxml.jackson.annotation.JsonTypeInfo

@JsonTypeInfo(
    include = JsonTypeInfo.As.PROPERTY,
    use = JsonTypeInfo.Id.NAME,
    property = "instanceType",
    visible = true,
)
sealed interface GeneralError : SessionError, LivenessError, DocumentScanError {
    class ServerError(val message: String? = null, val cause: Exception? = null) : GeneralError
    object InvalidOperationId : GeneralError
}

@JsonTypeInfo(
    include = JsonTypeInfo.As.PROPERTY,
    use = JsonTypeInfo.Id.NAME,
    property = "instanceType",
    visible = true,
)
sealed interface LivenessError {
    object AlreadyProcessed : LivenessError
    object InvalidLivenessState : LivenessError
    object DuplicatedExternalId : LivenessError
    object FaceScanReplayed : LivenessError
    object LivenessNotFound : LivenessError
}

@JsonTypeInfo(
    include = JsonTypeInfo.As.PROPERTY,
    use = JsonTypeInfo.Id.NAME,
    property = "instanceType",
    visible = true,
)
sealed interface SessionError {
    object NotProcessable : SessionError
}

@JsonTypeInfo(
    include = JsonTypeInfo.As.PROPERTY,
    use = JsonTypeInfo.Id.NAME,
    property = "instanceType",
    visible = true,
)
sealed interface DocumentScanError {
    object AlreadyProcessed : DocumentScanError
    object InvalidState : DocumentScanError
    object DocumentScanReplayed : DocumentScanError
}