package ai.friday.liveness.app.documentscan

import ai.friday.liveness.ByteWrapper
import ai.friday.liveness.app.liveness.ExternalId
import ai.friday.liveness.isUUID
import java.math.BigInteger
import java.security.MessageDigest
import java.util.UUID

enum class DocumentScanStatus {
    CREATED, PROCESSED
}

data class DocumentScanId(val value: String) {
    init {
        require(value.startsWith(PREFIX)) { "Should have prefix $PREFIX" }
        require(isUUID(value.removePrefix(PREFIX))) { "Should have uuid suffix" }
    }

    companion object {
        private const val PREFIX = "DOCUMENT_SCAN_ID-"
        fun generate(): DocumentScanId =
            DocumentScanId(PREFIX + UUID.randomUUID().toString())
    }
}

data class CreateDocumentScanRequest(
    val externalId: ExternalId,
    val clientId: String,
)

data class DocumentScanRequest(
    val id: DocumentScanId,
    val idScan: String,
    val frontImage: String?,
    val backImage: String?,
    val deviceKeyIdentifier: String,
    val userAgent: String,
    val clientIpAddress: String,
)

data class DocumentScanSecurityCheck(
    val documentScanCheck: Boolean,
    val auditTrailVerificationCheck: Boolean,
    val sessionTokenCheck: Boolean,
)

fun DocumentScanSecurityCheck.hasPassed(): Boolean =
    documentScanCheck && auditTrailVerificationCheck && sessionTokenCheck

sealed class DocumentScanResult {
    data class Success(
        val scannedIDPhotoFaceFoundWithMinimumQuality: Boolean,
        val didCompleteIDScanWithoutMatchingOCRTemplate: Boolean,
        val faceOnDocument: FaceOnDocumentStatus,
        val textOnDocument: TextOnDocumentStatus,
        val mrzStatus: MrzStatus,
        val digitalSpoofStatus: DigitalSpoofStatus,
        val documentData: String,
        val sessionExtraFlags: String,
        val securityCheck: DocumentScanSecurityCheck,
        val isDone: Boolean,
        val nextStep: NextStep,
        val blob: String,
    ) : DocumentScanResult()

    data class Failed(
        val securityCheck: DocumentScanSecurityCheck,
        val nextStep: NextStep,
        val blob: String,
    ) : DocumentScanResult()
}

enum class NextStep {
    FRONT_RETRY,
    BACK,
    BACK_RETRY,
    USER_CONFIRM,
    COMPLETE,
    NFC,
}

enum class DigitalSpoofStatus {
    LIKELY_PHYSICAL_ID,
    COULD_NOT_CONFIDENTLY_DETERMINE_PHYSICAL_ID_USER_NEEDS_TO_RETRY,
}

enum class MrzStatus {
    NO_MRZ_SPECIFIED_BY_TEMPLATE,
    MRZ_READ_BUT_CHECKSUM_FAILED,
    SUCCESS,
}

enum class TextOnDocumentStatus {
    NOT_AVAILABLE,
    LIKELY_ORIGINAL_TEXT,
    CANNOT_CONFIRM_ID_IS_AUTHENTIC,
}

enum class FaceOnDocumentStatus {
    NOT_AVAILABLE,
    LIKELY_ORIGINAL_FACE,
    CANNOT_CONFIRM_ID_IS_AUTHENTIC,
    OCR_TEMPLATE_DOES_NOT_SUPPORT_DETECTION,
}

fun digestDocumentScan(idScan: String): String {
    val hashBytes = MessageDigest
        .getInstance("SHA-256")
        .digest(ByteWrapper(idScan).bytes)

    return BigInteger(1, hashBytes).toString(16)
}