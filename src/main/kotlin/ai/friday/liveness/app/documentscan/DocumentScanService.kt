package ai.friday.liveness.app.documentscan

import ai.friday.liveness.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.liveness.ByteWrapper
import ai.friday.liveness.app.LivenessClientId
import ai.friday.liveness.app.errors.DocumentScanError
import ai.friday.liveness.app.errors.GeneralError
import arrow.core.Either
import arrow.core.left
import arrow.core.right
import io.micronaut.objectstorage.aws.AwsS3Operations
import io.micronaut.objectstorage.request.UploadRequest
import io.micronaut.objectstorage.response.UploadResponse
import jakarta.inject.Singleton
import java.time.ZonedDateTime
import software.amazon.awssdk.services.s3.model.PutObjectResponse

@Singleton
class DocumentScanService(
    private val objectStorageConfiguration: ObjectStorageConfiguration,
    private val objectStorage: AwsS3Operations,
    private val repository: DocumentScanEventRepository,
    private val adapter: DocumentScanAdapter,
    private val objectLinkGenerator: ObjectLinkGenerator,
) {
    suspend fun create(request: CreateDocumentScanRequest): Either<DocumentScanError, DocumentScan> {
        val documentScanCreated = DocumentScanCreated(
            id = DocumentScanId.generate(),
            created = getZonedDateTime(),
            clientId = LivenessClientId(request.clientId),
            externalId = request.externalId,
        )
        repository.save(documentScanCreated)

        return DocumentScan.build(documentScanCreated).right()
    }

    suspend fun scan(request: DocumentScanRequest): Either<DocumentScanError, DocumentScanResult> {
        return withScan(request.id) { scan ->
            if (request.isReplay()) {
                DocumentScanError.DocumentScanReplayed.left()
            } else if (scan !is PendingDocumentScan && scan !is ProcessedDocumentScan) {
                DocumentScanError.InvalidState.left()
            } else {
                adapter.scan(request).fold(
                    ifLeft = { it.left() },
                    ifRight = {
                        handleScanResult(request = request, result = it).right()
                    },
                )
            }
        }
    }

    suspend fun getData(id: DocumentScanId): Either<DocumentGetDataError, CompletedDocumentScan> {
        val documentScan = repository.findByIdOrNull(id) ?: return DocumentGetDataError.NotFound.left()

        return when (documentScan) {
            is CompletedDocumentScan -> documentScan.right()
            is PendingSessionCreation,
            is ProcessedDocumentScan,
            is PendingDocumentScan,
            -> DocumentGetDataError.InvalidState.left()
        }
    }

    suspend fun getImages(id: DocumentScanId): Either<DocumentGetImagesError, Pair<String, String?>> {
        val documentScan = repository.findByIdOrNull(id) ?: return DocumentGetImagesError.NotFound.left()
        return when (documentScan) {
            is CompletedDocumentScan -> {
                val frontImage = documentScan.frontImage.let { objectLinkGenerator.getObjectPresigned(bucketName = documentScan.documentsBucket, keyName = it) }
                val backImage = documentScan.backImage?.let { objectLinkGenerator.getObjectPresigned(bucketName = documentScan.documentsBucket, keyName = it) }
                (frontImage to backImage).right()
            }

            is PendingSessionCreation,
            is ProcessedDocumentScan,
            is PendingDocumentScan,
            -> DocumentGetImagesError.InvalidState.left()
        }
    }

    private suspend fun DocumentScanRequest.isReplay() =
        repository.existsIdScanDigest(digestDocumentScan(idScan))

    private fun upload(bytes: ByteArray, key: String, contentType: String? = null): UploadResponse<PutObjectResponse>? {
        val uploadRequest = UploadRequest.fromBytes(bytes, key)
        contentType?.let { uploadRequest.setContentType(contentType) }
        return objectStorage.upload(uploadRequest)
    }

    private fun DocumentScanResult.generateStoredObjectBlob(id: DocumentScanId, time: ZonedDateTime): String {
        return when (this) {
            is DocumentScanResult.Failed -> {
                generateFileName(id, "blob", this.blob, time).let { (key, bytes) ->
                    upload(bytes, key)
                    key
                }
            }

            is DocumentScanResult.Success -> {
                generateFileName(id, "blob", this.blob, time).let { (key, bytes) ->
                    upload(bytes, key)
                    key
                }
            }
        }
    }

    private fun DocumentScanRequest.generateStoredObject(type: StoredObjectType, time: ZonedDateTime): String? {
        return when (type) {
            StoredObjectType.FRONT_IMAGE -> {
                this.frontImage?.let { generateFileName(id, "front_image", it, time, "jpeg") }
            }

            StoredObjectType.BACK_IMAGE -> {
                this.backImage?.let { generateFileName(id, "back_image", it, time, "jpeg") }
            }

            StoredObjectType.ID_SCAN -> {
                generateFileName(id, "id_scan", idScan, time)
            }
        }?.let { (key, bytes) ->
            upload(bytes, key, "image/jpeg")
            key
        }
    }

    private suspend fun handleScanResult(request: DocumentScanRequest, result: DocumentScanResult): DocumentScanResult {
        return when (result) {
            is DocumentScanResult.Failed -> result
            is DocumentScanResult.Success -> {
                val created = getZonedDateTime()
                val event = when (result.nextStep) {
                    NextStep.COMPLETE -> DocumentScanCompleted(
                        id = request.id,
                        created = created,
                        idScan = request.generateStoredObject(StoredObjectType.ID_SCAN, created) ?: "",
                        idScanDigest = digestDocumentScan(request.idScan),
                        frontImage = request.generateStoredObject(StoredObjectType.FRONT_IMAGE, created) ?: throw IllegalStateException("front image must exist on DocumentScanCompleted"),
                        backImage = request.generateStoredObject(StoredObjectType.BACK_IMAGE, created),
                        scannedIDPhotoFaceFoundWithMinimumQuality = result.scannedIDPhotoFaceFoundWithMinimumQuality,
                        didCompleteIDScanWithoutMatchingOCRTemplate = result.didCompleteIDScanWithoutMatchingOCRTemplate,
                        faceOnDocument = result.faceOnDocument,
                        textOnDocument = result.textOnDocument,
                        mrzStatus = result.mrzStatus,
                        digitalSpoofStatus = result.digitalSpoofStatus,
                        documentData = result.documentData,
                        sessionExtraFlags = result.sessionExtraFlags,
                        securityCheck = result.securityCheck,
                        isDone = result.isDone,
                        nextStep = result.nextStep,
                        blob = result.generateStoredObjectBlob(request.id, created),
                        documentsRegion = objectStorageConfiguration.region,
                        documentsBucket = objectStorageConfiguration.bucket,
                    )

                    NextStep.BACK -> DocumentProcessed(
                        id = request.id,
                        created = created,
                        idScan = request.generateStoredObject(StoredObjectType.ID_SCAN, created) ?: "",
                        idScanDigest = digestDocumentScan(request.idScan),
                        frontImage = request.generateStoredObject(StoredObjectType.FRONT_IMAGE, created),
                        backImage = request.generateStoredObject(StoredObjectType.BACK_IMAGE, created),
                        scannedIDPhotoFaceFoundWithMinimumQuality = result.scannedIDPhotoFaceFoundWithMinimumQuality,
                        didCompleteIDScanWithoutMatchingOCRTemplate = result.didCompleteIDScanWithoutMatchingOCRTemplate,
                        faceOnDocument = result.faceOnDocument,
                        textOnDocument = result.textOnDocument,
                        mrzStatus = result.mrzStatus,
                        digitalSpoofStatus = result.digitalSpoofStatus,
                        documentData = result.documentData,
                        sessionExtraFlags = result.sessionExtraFlags,
                        securityCheck = result.securityCheck,
                        isDone = result.isDone,
                        nextStep = result.nextStep,
                        blob = result.generateStoredObjectBlob(request.id, created),
                        documentsRegion = objectStorageConfiguration.region,
                        documentsBucket = objectStorageConfiguration.bucket,
                    )

                    NextStep.FRONT_RETRY -> DocumentProcessed(
                        id = request.id,
                        created = created,
                        idScan = request.generateStoredObject(StoredObjectType.ID_SCAN, created) ?: "",
                        idScanDigest = digestDocumentScan(request.idScan),
                        frontImage = request.generateStoredObject(StoredObjectType.FRONT_IMAGE, created),
                        backImage = request.generateStoredObject(StoredObjectType.BACK_IMAGE, created),
                        scannedIDPhotoFaceFoundWithMinimumQuality = result.scannedIDPhotoFaceFoundWithMinimumQuality,
                        didCompleteIDScanWithoutMatchingOCRTemplate = result.didCompleteIDScanWithoutMatchingOCRTemplate,
                        faceOnDocument = result.faceOnDocument,
                        textOnDocument = result.textOnDocument,
                        mrzStatus = result.mrzStatus,
                        digitalSpoofStatus = result.digitalSpoofStatus,
                        documentData = result.documentData,
                        sessionExtraFlags = result.sessionExtraFlags,
                        securityCheck = result.securityCheck,
                        isDone = result.isDone,
                        nextStep = result.nextStep,
                        blob = result.generateStoredObjectBlob(request.id, created),
                        documentsRegion = objectStorageConfiguration.region,
                        documentsBucket = objectStorageConfiguration.bucket,
                    )

                    NextStep.BACK_RETRY -> DocumentProcessed(
                        id = request.id,
                        created = created,
                        idScan = request.generateStoredObject(StoredObjectType.ID_SCAN, created) ?: "",
                        idScanDigest = digestDocumentScan(request.idScan),
                        scannedIDPhotoFaceFoundWithMinimumQuality = result.scannedIDPhotoFaceFoundWithMinimumQuality,
                        didCompleteIDScanWithoutMatchingOCRTemplate = result.didCompleteIDScanWithoutMatchingOCRTemplate,
                        faceOnDocument = result.faceOnDocument,
                        textOnDocument = result.textOnDocument,
                        mrzStatus = result.mrzStatus,
                        digitalSpoofStatus = result.digitalSpoofStatus,
                        documentData = result.documentData,
                        sessionExtraFlags = result.sessionExtraFlags,
                        securityCheck = result.securityCheck,
                        isDone = result.isDone,
                        nextStep = result.nextStep,
                        blob = result.generateStoredObjectBlob(request.id, created),
                        frontImage = request.generateStoredObject(StoredObjectType.FRONT_IMAGE, created),
                        backImage = request.generateStoredObject(StoredObjectType.BACK_IMAGE, created),
                        documentsRegion = objectStorageConfiguration.region,
                        documentsBucket = objectStorageConfiguration.bucket,
                    )

                    NextStep.NFC, NextStep.USER_CONFIRM -> TODO()
                }

                repository.save(event)
                result
            }
        }
    }

    private suspend fun <T> withScan(
        documentScanId: DocumentScanId,
        toBeExecuted: suspend (documentScan: DocumentScan) -> Either<DocumentScanError, T>,
    ): Either<DocumentScanError, T> {
        val scan = repository.findByIdOrNull(documentScanId) ?: return GeneralError.InvalidOperationId.left()

        if (scan.status != DocumentScanStatus.CREATED) {
            return DocumentScanError.AlreadyProcessed.left()
        }

        return toBeExecuted(scan)
    }
}

private fun generateFileName(id: DocumentScanId, name: String, file: String, time: ZonedDateTime, extension: String? = null) =
    "${id.value}/$name-${time.toInstant().toEpochMilli()}${extension?.let { ".$extension" } ?: ""}" to ByteWrapper(file).bytes

private enum class StoredObjectType {
    FRONT_IMAGE, BACK_IMAGE, ID_SCAN
}

sealed class DocumentGetDataError {
    object NotFound : DocumentGetDataError()
    object InvalidState : DocumentGetDataError()
}

sealed class DocumentGetImagesError {
    object NotFound : DocumentGetImagesError()
    object InvalidState : DocumentGetImagesError()
    data class Error(val e: Exception) : DocumentGetImagesError()
}