package ai.friday.liveness.app.documentscan

import ai.friday.liveness.app.LivenessClientId
import ai.friday.liveness.app.liveness.ExternalId
import java.time.ZonedDateTime

sealed class DocumentScan {
    abstract val id: DocumentScanId
    abstract val externalId: ExternalId
    abstract val createdAt: ZonedDateTime
    abstract val updatedAt: ZonedDateTime
    abstract val clientId: LivenessClientId

    open val status = DocumentScanStatus.CREATED

    open fun apply(event: DocumentScanCreated): DocumentScan {
        throw IllegalStateException("TODO")
    }

    open fun apply(event: DocumentScanSessionCreated): DocumentScan {
        throw IllegalStateException("TODO")
    }

    open fun apply(event: DocumentProcessed): DocumentScan {
        throw IllegalStateException("TODO")
    }

    open fun apply(event: DocumentScanCompleted): DocumentScan {
        throw IllegalStateException("TODO")
    }

    open fun apply(event: DocumentScanError): DocumentScan {
        throw IllegalStateException("TODO")
    }

    fun apply(event: DocumentScanEvent): DocumentScan {
        return when (event) {
            is DocumentScanCreated -> apply(event)
            is DocumentScanSessionCreated -> apply(event)
            is DocumentProcessed -> apply(event)
            is DocumentScanCompleted -> apply(event)
            is DocumentScanError -> apply(event)
        }
    }

    companion object {

        fun build(history: List<DocumentScanEvent>): DocumentScan {
            if (history.isEmpty()) {
                throw IllegalStateException("Cannot create document scan from empty history")
            }

            val creationEvent = history.first()
            val otherEvents = history.takeLast(history.size - 1)

            return otherEvents.fold(of(creationEvent)) { instance: DocumentScan, event ->
                instance.apply(event)
            }
        }

        fun build(vararg history: DocumentScanEvent): DocumentScan {
            return build(history.toList())
        }

        private fun of(event: DocumentScanEvent): PendingSessionCreation {
            return when (event) {
                is DocumentScanCreated -> PendingSessionCreation(event)
                is DocumentProcessed, is DocumentScanCompleted, is DocumentScanError, is DocumentScanSessionCreated -> throw IllegalStateException("First document scan event must be DocumentScanCreated")
            }
        }
    }
}

data class PendingSessionCreation(
    override val id: DocumentScanId,
    override val externalId: ExternalId,
    override val createdAt: ZonedDateTime,
    override val updatedAt: ZonedDateTime,
    override val clientId: LivenessClientId,
) : DocumentScan() {
    internal constructor(event: DocumentScanCreated) : this(
        id = event.id,
        externalId = event.externalId,
        createdAt = event.created,
        updatedAt = event.created,
        clientId = event.clientId,
    )

    override fun apply(event: DocumentScanSessionCreated) = PendingDocumentScan(
        id = event.id,
        externalId = this.externalId,
        createdAt = this.createdAt,
        updatedAt = event.created,
        clientId = this.clientId,
        deviceKeyIdentifier = event.deviceKeyIdentifier,
        userAgent = event.userAgent,
        clientIpAddress = event.clientIpAddress,
    )
}

data class PendingDocumentScan(
    override val id: DocumentScanId,
    override val externalId: ExternalId,
    override val createdAt: ZonedDateTime,
    override val updatedAt: ZonedDateTime,
    override val clientId: LivenessClientId,
    val deviceKeyIdentifier: String,
    val userAgent: String,
    val clientIpAddress: String,
) : DocumentScan() {
    override fun apply(event: DocumentProcessed) = ProcessedDocumentScan(
        id = event.id,
        externalId = this.externalId,
        createdAt = this.createdAt,
        updatedAt = event.created,
        clientId = this.clientId,
        deviceKeyIdentifier = this.deviceKeyIdentifier,
        userAgent = this.userAgent,
        clientIpAddress = this.clientIpAddress,
        idScan = event.idScan,
        frontImage = event.frontImage,
        backImage = event.backImage,
        scannedIDPhotoFaceFoundWithMinimumQuality = event.scannedIDPhotoFaceFoundWithMinimumQuality,
        didCompleteIDScanWithoutMatchingOCRTemplate = event.didCompleteIDScanWithoutMatchingOCRTemplate,
        faceOnDocument = event.faceOnDocument,
        textOnDocument = event.textOnDocument,
        mrzStatus = event.mrzStatus,
        digitalSpoofStatus = event.digitalSpoofStatus,
        documentData = event.documentData,
        sessionExtraFlags = event.sessionExtraFlags,
        securityCheck = event.securityCheck,
        isDone = event.isDone,
        nextStep = event.nextStep,
        blob = event.blob,
    )

    override fun apply(event: DocumentScanCompleted) = CompletedDocumentScan(
        id = event.id,
        externalId = this.externalId,
        createdAt = this.createdAt,
        updatedAt = event.created,
        clientId = this.clientId,
        deviceKeyIdentifier = this.deviceKeyIdentifier,
        userAgent = this.userAgent,
        clientIpAddress = this.clientIpAddress,
        idScan = event.idScan,
        frontImage = event.frontImage,
        backImage = event.backImage,
        scannedIDPhotoFaceFoundWithMinimumQuality = event.scannedIDPhotoFaceFoundWithMinimumQuality,
        didCompleteIDScanWithoutMatchingOCRTemplate = event.didCompleteIDScanWithoutMatchingOCRTemplate,
        faceOnDocument = event.faceOnDocument,
        textOnDocument = event.textOnDocument,
        mrzStatus = event.mrzStatus,
        digitalSpoofStatus = event.digitalSpoofStatus,
        documentData = event.documentData,
        sessionExtraFlags = event.sessionExtraFlags,
        securityCheck = event.securityCheck,
        isDone = event.isDone,
        nextStep = event.nextStep,
        blob = event.blob,
        documentsRegion = event.documentsRegion,
        documentsBucket = event.documentsBucket,
    )

    override fun apply(event: DocumentScanError) = PendingDocumentScan(
        id = event.id,
        externalId = this.externalId,
        createdAt = this.createdAt,
        updatedAt = event.created,
        clientId = this.clientId,
        deviceKeyIdentifier = this.deviceKeyIdentifier,
        userAgent = this.userAgent,
        clientIpAddress = this.clientIpAddress,
    )
}

data class ProcessedDocumentScan(
    override val id: DocumentScanId,
    override val externalId: ExternalId,
    override val createdAt: ZonedDateTime,
    override val updatedAt: ZonedDateTime,
    override val clientId: LivenessClientId,
    val deviceKeyIdentifier: String,
    val userAgent: String,
    val clientIpAddress: String,
    val idScan: String,
    val frontImage: String?,
    val backImage: String?,
    val scannedIDPhotoFaceFoundWithMinimumQuality: Boolean,
    val didCompleteIDScanWithoutMatchingOCRTemplate: Boolean,
    val faceOnDocument: FaceOnDocumentStatus,
    val textOnDocument: TextOnDocumentStatus,
    val mrzStatus: MrzStatus,
    val digitalSpoofStatus: DigitalSpoofStatus,
    val documentData: String,
    val sessionExtraFlags: String,
    val securityCheck: DocumentScanSecurityCheck,
    val isDone: Boolean,
    val nextStep: NextStep,
    val blob: String,
) : DocumentScan() {
    override fun apply(event: DocumentProcessed) = ProcessedDocumentScan(
        id = event.id,
        externalId = this.externalId,
        createdAt = this.createdAt,
        updatedAt = event.created,
        clientId = this.clientId,
        deviceKeyIdentifier = this.deviceKeyIdentifier,
        userAgent = this.userAgent,
        clientIpAddress = this.clientIpAddress,
        idScan = event.idScan,
        frontImage = event.frontImage,
        backImage = event.backImage,
        scannedIDPhotoFaceFoundWithMinimumQuality = event.scannedIDPhotoFaceFoundWithMinimumQuality,
        didCompleteIDScanWithoutMatchingOCRTemplate = event.didCompleteIDScanWithoutMatchingOCRTemplate,
        faceOnDocument = event.faceOnDocument,
        textOnDocument = event.textOnDocument,
        mrzStatus = event.mrzStatus,
        digitalSpoofStatus = event.digitalSpoofStatus,
        documentData = event.documentData,
        sessionExtraFlags = event.sessionExtraFlags,
        securityCheck = event.securityCheck,
        isDone = event.isDone,
        nextStep = event.nextStep,
        blob = event.blob,
    )

    override fun apply(event: DocumentScanCompleted) = CompletedDocumentScan(
        id = event.id,
        externalId = this.externalId,
        createdAt = this.createdAt,
        updatedAt = event.created,
        clientId = this.clientId,
        deviceKeyIdentifier = this.deviceKeyIdentifier,
        userAgent = this.userAgent,
        clientIpAddress = this.clientIpAddress,
        idScan = event.idScan,
        frontImage = event.frontImage,
        backImage = event.backImage,
        scannedIDPhotoFaceFoundWithMinimumQuality = event.scannedIDPhotoFaceFoundWithMinimumQuality,
        didCompleteIDScanWithoutMatchingOCRTemplate = event.didCompleteIDScanWithoutMatchingOCRTemplate,
        faceOnDocument = event.faceOnDocument,
        textOnDocument = event.textOnDocument,
        mrzStatus = event.mrzStatus,
        digitalSpoofStatus = event.digitalSpoofStatus,
        documentData = event.documentData,
        sessionExtraFlags = event.sessionExtraFlags,
        securityCheck = event.securityCheck,
        isDone = event.isDone,
        nextStep = event.nextStep,
        blob = event.blob,
        documentsRegion = event.documentsRegion,
        documentsBucket = event.documentsBucket,
    )

    override fun apply(event: DocumentScanError) = ProcessedDocumentScan(
        id = event.id,
        externalId = this.externalId,
        createdAt = this.createdAt,
        updatedAt = event.created,
        clientId = this.clientId,
        deviceKeyIdentifier = this.deviceKeyIdentifier,
        userAgent = this.userAgent,
        clientIpAddress = this.clientIpAddress,
        idScan = event.idScan,
        frontImage = event.frontImage,
        backImage = event.backImage,
        scannedIDPhotoFaceFoundWithMinimumQuality = this.scannedIDPhotoFaceFoundWithMinimumQuality,
        didCompleteIDScanWithoutMatchingOCRTemplate = this.didCompleteIDScanWithoutMatchingOCRTemplate,
        faceOnDocument = this.faceOnDocument,
        textOnDocument = this.textOnDocument,
        mrzStatus = this.mrzStatus,
        digitalSpoofStatus = this.digitalSpoofStatus,
        documentData = this.documentData,
        sessionExtraFlags = this.sessionExtraFlags,
        securityCheck = event.securityCheck,
        isDone = this.isDone,
        nextStep = event.nextStep,
        blob = event.blob,
    )
}

data class CompletedDocumentScan(
    override val id: DocumentScanId,
    override val externalId: ExternalId,
    override val createdAt: ZonedDateTime,
    override val updatedAt: ZonedDateTime,
    override val clientId: LivenessClientId,
    val deviceKeyIdentifier: String,
    val userAgent: String,
    val clientIpAddress: String,
    val idScan: String,
    val frontImage: String,
    val backImage: String?,
    val scannedIDPhotoFaceFoundWithMinimumQuality: Boolean,
    val didCompleteIDScanWithoutMatchingOCRTemplate: Boolean,
    val faceOnDocument: FaceOnDocumentStatus,
    val textOnDocument: TextOnDocumentStatus,
    val mrzStatus: MrzStatus,
    val digitalSpoofStatus: DigitalSpoofStatus,
    val documentData: String,
    val sessionExtraFlags: String,
    val securityCheck: DocumentScanSecurityCheck,
    val isDone: Boolean,
    val nextStep: NextStep,
    val blob: String,
    val documentsRegion: String,
    val documentsBucket: String,
) : DocumentScan() {
    override val status = DocumentScanStatus.PROCESSED
}