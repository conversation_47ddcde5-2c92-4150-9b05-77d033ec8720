package ai.friday.liveness.app.documentscan

import ai.friday.liveness.app.LivenessClientId
import ai.friday.liveness.app.liveness.ExternalId
import java.time.ZonedDateTime

sealed class DocumentScanEvent {
    abstract val id: DocumentScanId
    abstract val created: ZonedDateTime
    abstract val eventType: DocumentScanEventType
}

data class DocumentScanCreated(
    override val id: DocumentScanId,
    override val created: ZonedDateTime,
    val clientId: LivenessClientId,
    val externalId: ExternalId,
) : DocumentScanEvent() {
    override val eventType = DocumentScanEventType.CREATED
}

data class DocumentScanSessionCreated(
    override val id: DocumentScanId,
    override val created: ZonedDateTime,
    val deviceKeyIdentifier: String,
    val userAgent: String,
    val clientIpAddress: String,
) : DocumentScanEvent() {
    override val eventType = DocumentScanEventType.SESSION_CREATED
}

data class DocumentProcessed(
    override val id: DocumentScanId,
    override val created: ZonedDateTime,
    val idScan: String,
    val idScanDigest: String,
    val frontImage: String?,
    val backImage: String?,
    val scannedIDPhotoFaceFoundWithMinimumQuality: Boolean,
    val didCompleteIDScanWithoutMatchingOCRTemplate: Boolean,
    val faceOnDocument: FaceOnDocumentStatus,
    val textOnDocument: TextOnDocumentStatus,
    val mrzStatus: MrzStatus,
    val digitalSpoofStatus: DigitalSpoofStatus,
    val documentData: String,
    val sessionExtraFlags: String,
    val securityCheck: DocumentScanSecurityCheck,
    val isDone: Boolean,
    val nextStep: NextStep,
    val blob: String,
    val documentsRegion: String,
    val documentsBucket: String,
) : DocumentScanEvent() {
    override val eventType = DocumentScanEventType.PROCESSED
}

data class DocumentScanCompleted(
    override val id: DocumentScanId,
    override val created: ZonedDateTime,
    val idScan: String,
    val idScanDigest: String,
    val frontImage: String,
    val backImage: String?,
    val scannedIDPhotoFaceFoundWithMinimumQuality: Boolean,
    val didCompleteIDScanWithoutMatchingOCRTemplate: Boolean,
    val faceOnDocument: FaceOnDocumentStatus,
    val textOnDocument: TextOnDocumentStatus,
    val mrzStatus: MrzStatus,
    val digitalSpoofStatus: DigitalSpoofStatus,
    val documentData: String,
    val sessionExtraFlags: String,
    val securityCheck: DocumentScanSecurityCheck,
    val isDone: Boolean,
    val nextStep: NextStep,
    val blob: String,
    val documentsRegion: String,
    val documentsBucket: String,
) : DocumentScanEvent() {
    override val eventType = DocumentScanEventType.COMPLETED
}

data class DocumentScanError(
    override val id: DocumentScanId,
    override val created: ZonedDateTime,
    val idScan: String,
    val idScanDigest: String,
    val frontImage: String?,
    val backImage: String?,
    val securityCheck: DocumentScanSecurityCheck,
    val nextStep: NextStep,
    val blob: String,
    val documentsRegion: String,
    val documentsBucket: String,
) : DocumentScanEvent() {
    override val eventType = DocumentScanEventType.ERROR
}

enum class DocumentScanEventType {
    CREATED, SESSION_CREATED, PROCESSED, COMPLETED, ERROR
}