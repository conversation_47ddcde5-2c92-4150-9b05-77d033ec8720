package ai.friday.liveness.app.documentscan

import ai.friday.liveness.app.errors.DocumentScanError
import arrow.core.Either

interface DocumentScanEventRepository {
    suspend fun save(event: DocumentScanEvent)
    suspend fun findByIdOrNull(scan: DocumentScanId): DocumentScan?
    suspend fun existsIdScanDigest(idScanDigest: String): Boolean
}

interface DocumentScanAdapter {
    suspend fun scan(request: DocumentScanRequest): Either<DocumentScanError, DocumentScanResult>
}

interface ObjectStorageConfiguration {
    val region: String
    val bucket: String
}

interface ObjectLinkGenerator {
    suspend fun getObjectPresigned(bucketName: String, keyName: String): String
}