package ai.friday.liveness

import kotlin.system.measureTimeMillis

inline fun <T> measure(fn: () -> T): Pair<T, Long> {
    val result: T
    val elapsed = measureTimeMillis { result = fn() }
    return result to elapsed
}

data class MeasureResult<T>(val result: T, val elapsed: Long)

inline fun <T> measureTimeInMillis(fn: () -> T): MeasureResult<T> {
    val (result, elapsed) = measure { fn() }
    return MeasureResult(result, elapsed)
}