package ai.friday.liveness

import java.time.Instant
import kotlin.time.DurationUnit
import kotlin.time.toDuration
import net.logstash.logback.marker.Markers
import software.amazon.awssdk.services.sqs.model.Message
import software.amazon.awssdk.services.sqs.model.MessageSystemAttributeName

fun Message.getCreatedAtInMillis(): Long? =
    attributes()[MessageSystemAttributeName.APPROXIMATE_FIRST_RECEIVE_TIMESTAMP]?.toLong()

fun Message.getAttempts(): Int =
    attributes()[MessageSystemAttributeName.APPROXIMATE_RECEIVE_COUNT]?.toInt() ?: 1

fun Message.getAgeInMinutes(): Long? = getCreatedAtInMillis()?.let { createdAt ->
    (Instant.now().toEpochMilli() - createdAt)
        .toDuration(DurationUnit.MILLISECONDS)
        .inWholeMinutes
}

fun Message.markers() = Markers.append("eventBody", body())
    .andAppend("receiveCount", getAttempts())
    .andAppend("createdAt", getCreatedAtInMillis())
    .andAppend("ageInMinutes", getAgeInMinutes())