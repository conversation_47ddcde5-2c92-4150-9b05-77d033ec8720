package ai.friday.liveness

import java.time.LocalDate
import java.time.ZoneId
import java.time.ZonedDateTime
import java.time.format.DateTimeFormatter

val brazilTimeZone: ZoneId = ZoneId.of("Brazil/East")
val dateTimeFormat: DateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")

object BrazilZonedDateTimeSupplier {
    fun getZonedDateTime(): ZonedDateTime = ZonedDateTime.now(brazilTimeZone)
    fun getLocalDate(): LocalDate = getZonedDateTime().toLocalDate()
}