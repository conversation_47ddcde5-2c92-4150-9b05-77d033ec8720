package ai.friday.liveness.adapters.http

import io.micronaut.http.HttpResponse
import io.micronaut.http.HttpStatus
import io.micronaut.http.MutableHttpResponse

interface HttpErrorResponse {
    val code: Int
    val message: String
}

val genericHttpErrorResponse: MutableHttpResponse<ResponseTO> =
    HttpResponse.serverError(ResponseTO(5000, "Ocorreu um erro inesperado"))

fun HttpErrorResponse.asBadRequest(): MutableHttpResponse<ResponseTO> = HttpResponse.badRequest(this.toResponse())
fun HttpErrorResponse.asConflict(): MutableHttpResponse<ResponseTO> =
    HttpResponse.status<ResponseTO?>(HttpStatus.CONFLICT).body(this.toResponse())

private fun HttpErrorResponse.toResponse() = ResponseTO(code, message)

data class ResponseTO(val code: Int, val message: String)