package ai.friday.liveness.adapters.messaging

import ai.friday.liveness.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.liveness.andAppend
import ai.friday.liveness.app.integrations.MessageHandlerConfiguration
import io.micronaut.context.annotation.ConfigurationInject
import io.micronaut.context.annotation.ConfigurationProperties
import io.micronaut.context.event.ApplicationEventListener
import io.micronaut.discovery.event.ServiceReadyEvent
import io.micronaut.health.HealthStatus
import io.micronaut.management.health.indicator.HealthIndicator
import io.micronaut.management.health.indicator.HealthResult
import io.micronaut.runtime.event.ApplicationShutdownEvent
import io.micronaut.scheduling.cron.CronExpression
import io.micronaut.tracing.annotation.NewSpan
import io.reactivex.Flowable
import java.time.Duration
import java.time.Instant
import java.time.format.DateTimeFormatter
import javax.annotation.PreDestroy
import kotlin.concurrent.thread
import kotlinx.coroutines.delay
import kotlinx.coroutines.runBlocking
import net.logstash.logback.marker.LogstashMarker
import net.logstash.logback.marker.Markers
import org.reactivestreams.Publisher
import org.slf4j.LoggerFactory
import software.amazon.awssdk.services.sqs.SqsClient
import software.amazon.awssdk.services.sqs.model.CreateQueueRequest
import software.amazon.awssdk.services.sqs.model.DeleteMessageRequest
import software.amazon.awssdk.services.sqs.model.GetQueueUrlRequest
import software.amazon.awssdk.services.sqs.model.ListQueuesRequest
import software.amazon.awssdk.services.sqs.model.Message
import software.amazon.awssdk.services.sqs.model.QueueAttributeName
import software.amazon.awssdk.services.sqs.model.ReceiveMessageRequest
import software.amazon.awssdk.services.sqs.model.SetQueueAttributesRequest

internal const val eventTypeAttributeName = "eventType"

@ConfigurationProperties("aws.sqs")
class SQSProperties @ConfigurationInject constructor(
    val sqsWaitTime: Int,
    val visibilityTimeout: Int,
    val maxNumberOfMessages: Int,
    val dlqArn: String,
    val sqsCoolDownTime: Int,

)

abstract class AbstractSQSHandler(
    private val amazonSQS: SqsClient,
    private val sqsProperties: SQSProperties,
    private val configuration: MessageHandlerConfiguration,
) : ApplicationEventListener<Any>, HealthIndicator {

    var isActive = false
        private set

    private val cronExpression: CronExpression? by lazy {
        configuration.timeWindowCron?.let {
            CronExpression.create(it)
        }
    }

    override fun onApplicationEvent(event: Any) {
        val markers = Markers.append("queueName", configuration.queueName).andAppend("configuration", configuration)
            .andAppend("eventName", event.javaClass.simpleName)

        when (event) {
            is ServiceReadyEvent -> {
                logger.info(markers, "AbstractSQSHandlerOnServiceReadyEvent")
                try {
                    createQueueIfNotExists()
                    isActive = true
                    repeat(configuration.consumers) { consumerId ->
                        logger.info(markers, "${configuration.queueName}-$consumerId-start")
                        receiveMessages(consumerId)
                    }
                } catch (e: Exception) {
                    logger.error(markers, "AbstractSQSHandlerOnServiceReadyEvent", e)
                    throw e
                }
            }

            is ApplicationShutdownEvent -> {
                isActive = false
                logger.info(
                    markers.andAppend("forceShutDown", event.source.isForceExit),
                    "AbstractSQSHandlerOnApplicationShutdownEvent",
                )
            }
        }
    }

    open fun receiveMessages(consumerId: Int = 0) {
        val threadName = "${this.javaClass.simpleName}-$consumerId"

        fun buildMarkers() =
            Markers.append("sqsHandlerThreadName", threadName).andAppend("queueName", configuration.queueName)

        logger.info(
            buildMarkers(),
            "SQSHandlerStarting",
        )
        val thread = thread(start = false, isDaemon = true, name = threadName, priority = 0) {
            logger.info(
                buildMarkers(),
                "SQSHandlerStarted",
            )
            receiveMessagesAsync(consumerId)
            logger.info(
                buildMarkers(),
                "SQSHandlerStopped",
            )
        }

        thread.setUncaughtExceptionHandler { _, exception ->
            logger.error(
                buildMarkers(),
                "SQSHandlerThreadException",
                exception,
            )
        }

        thread.start()
    }

    @NewSpan
    open fun receiveMessagesAsync(consumerId: Int = 0) {
        val queueUrl =
            amazonSQS.getQueueUrl(GetQueueUrlRequest.builder().queueName(configuration.queueName).build()).queueUrl()
        val receiveRequest =
            ReceiveMessageRequest.builder().queueUrl(queueUrl)
                .maxNumberOfMessages(sqsProperties.maxNumberOfMessages)
                .waitTimeSeconds(sqsProperties.sqsWaitTime)
                .messageAttributeNames(
                    eventTypeAttributeName,
                )
                .attributeNames(QueueAttributeName.ALL)
                .build()
        while (isActive) {
            if (waitForNexCronWindow()) continue

            val messages = amazonSQS.receiveMessage(receiveRequest).messages()

            if (messages.isNotEmpty()) {
                process(consumerId, messages, queueUrl)
            }

            if (messages.isEmpty()) {
                if (sqsProperties.sqsCoolDownTime > 0) {
                    Thread.sleep(sqsProperties.sqsCoolDownTime * 1000L)
                }
            }
        }
    }

    private fun Instant.elapsedSeconds() = Duration.between(this, getZonedDateTime().toInstant()).toSeconds()
    private fun Instant.elapsedMillis() = Duration.between(this, getZonedDateTime().toInstant()).toMillis()

    open fun process(consumerId: Int = 0, messages: List<Message>, queueUrl: String) {
        for (message in messages) {
            tryProcessMessage(message, queueUrl)
        }
    }

    @NewSpan
    open fun tryProcessMessage(message: Message, queueUrl: String) {
        val startInstant = getZonedDateTime().toInstant()
        val markers = Markers.append("queueName", configuration.queueName)
        try {
            val response = handleMessage(message)
            markers.andAppend("handleSeconds", startInstant.elapsedSeconds())
            if (response.shouldDeleteMessage) {
                message.delete(queueUrl, markers)
            }
        } catch (e: Exception) {
            val response = handleError(message, e)
            if (response.shouldDeleteMessage) {
                message.delete(queueUrl, markers)
            }
        }
        logger.info(
            markers.andAppend("elapsedSeconds", startInstant.elapsedSeconds()),
            "tryProcessMessage",
        )
    }

    private fun Message.delete(queueUrl: String, markers: LogstashMarker) {
        val startInstant = getZonedDateTime().toInstant()

        amazonSQS.deleteMessage(
            DeleteMessageRequest.builder().queueUrl(queueUrl).receiptHandle(this.receiptHandle()).build(),
        )

        markers.andAppend("deleteMilliseconds", startInstant.elapsedMillis())
    }

    abstract fun handleMessage(m: Message): SQSHandlerResponse

    abstract fun handleError(m: Message, e: Exception): SQSHandlerResponse

    override fun getResult(): Publisher<HealthResult> {
        val healthStatus = if (isActive) HealthStatus.UP else HealthStatus.DOWN
        return Flowable.just(HealthResult.builder("${this::class.simpleName}HealthIndicator", healthStatus).build())
    }

    private fun createQueueIfNotExists() {
        val listQueuesResult =
            amazonSQS.listQueues(ListQueuesRequest.builder().queueNamePrefix(configuration.queueName).build())
        if (listQueuesResult.queueUrls().isEmpty()) {
            val createQueueResult =
                amazonSQS.createQueue(CreateQueueRequest.builder().queueName(configuration.queueName).build())
            val request = SetQueueAttributesRequest.builder().queueUrl(createQueueResult.queueUrl()).attributes(
                mapOf(
                    QueueAttributeName.VISIBILITY_TIMEOUT to sqsProperties.visibilityTimeout.toString(),
                    QueueAttributeName.REDRIVE_POLICY to "{\"maxReceiveCount\":\"60\", \"deadLetterTargetArn\":\"${sqsProperties.dlqArn}\"}",
                ),
            ).build()

            amazonSQS.setQueueAttributes(request)
        }
    }

    private fun waitForNexCronWindow(): Boolean {
        val duration = cronExpression.calculateDurationUntilNextExecution(configuration.timeWindowToleranceInSeconds)
            ?: return false

        val markers =
            Markers.append("cron", configuration.timeWindowCron)
                .andAppend("cronToleranceInSeconds", configuration.timeWindowToleranceInSeconds)
                .andAppend("duration", duration)
                .andAppend(
                    "nextExecution",
                    getZonedDateTime().plusSeconds(duration.seconds).format(DateTimeFormatter.ISO_DATE_TIME),
                )

        runBlocking {
            logger.info(markers, "SQSHandler#Stopped")
            delay(duration.toMillis())
            logger.info(markers, "SQSHandler#Restarted")
        }

        return false
    }

    @PreDestroy
    fun tearDown() {
        isActive = false
    }

    override fun supports(event: Any) = true

    companion object {
        private val logger = LoggerFactory.getLogger(AbstractSQSHandler::class.java)
    }
}

data class SQSHandlerResponse(val shouldDeleteMessage: Boolean)

fun CronExpression?.calculateDurationUntilNextExecution(
    tolerance: Long,
): Duration? {
    this?.let { cronExpression ->
        val now = getZonedDateTime()
        val nextExecution = cronExpression.nextTimeAfter(now)

        val duration = Duration.between(now, nextExecution)
        if (duration.toSeconds() > tolerance) {
            return duration
        }
    }
    return null
}