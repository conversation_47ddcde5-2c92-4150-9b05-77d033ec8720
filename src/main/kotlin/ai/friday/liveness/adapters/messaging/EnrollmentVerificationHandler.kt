package ai.friday.liveness.adapters.messaging

import ai.friday.liveness.adapters.parser.parseObjectFrom
import ai.friday.liveness.andAppend
import ai.friday.liveness.app.integrations.MessageHandlerConfiguration
import ai.friday.liveness.app.integrations.enrollmentVerify
import ai.friday.liveness.app.liveness.CompletedLiveness
import ai.friday.liveness.app.liveness.DuplicationVerified
import ai.friday.liveness.app.liveness.FaceMapSearchAdapter
import ai.friday.liveness.app.liveness.FaceMatchGroup
import ai.friday.liveness.app.liveness.FraudVerified
import ai.friday.liveness.app.liveness.Liveness
import ai.friday.liveness.app.liveness.LivenessEventRepository
import ai.friday.liveness.app.liveness.LivenessId
import ai.friday.liveness.app.liveness.LivenessType
import ai.friday.liveness.app.liveness.isCompletedWithSuccess
import ai.friday.liveness.getAttempts
import ai.friday.liveness.markers
import arrow.core.Either
import arrow.core.getOrHandle
import arrow.core.left
import arrow.core.right
import io.micronaut.context.annotation.Requirements
import io.micronaut.context.annotation.Requires
import jakarta.inject.Named
import jakarta.inject.Singleton
import kotlin.contracts.ExperimentalContracts
import kotlinx.coroutines.runBlocking
import net.logstash.logback.marker.LogstashMarker
import org.slf4j.LoggerFactory
import software.amazon.awssdk.services.sqs.SqsClient
import software.amazon.awssdk.services.sqs.model.Message

@Singleton
@Requirements(Requires(notEnv = ["test"]))
open class EnrollmentVerificationHandler(
    sqsClient: SqsClient,
    sqsProperties: SQSProperties,
    @Named(enrollmentVerify) faceMapEnrollmentConfiguration: MessageHandlerConfiguration,
    private val livenessEventRepository: LivenessEventRepository,
    private val faceMapSearch: FaceMapSearchAdapter,
) : AbstractSQSHandler(
    sqsClient,
    sqsProperties,
    faceMapEnrollmentConfiguration,
) {
    private val logger = LoggerFactory.getLogger(EnrollmentVerificationHandler::class.java)

    override fun handleMessage(m: Message): SQSHandlerResponse {
        val livenessId = parseObjectFrom<LivenessId>(m.body())
        val markers = m.markers().andAppend("livenessId", livenessId.value)

        val liveness = runBlocking {
            livenessEventRepository.findByIdOrNull(livenessId)
        }

        val completedLiveness = checkIsCompletedLiveness(liveness, markers).getOrHandle { shouldRetry ->
            val shouldDeleteMessage = !shouldRetry || m.getAttempts() >= 3
            if (shouldDeleteMessage) {
                logger.error(markers, "EnrollmentVerifyDuplicationHandler")
            } else {
                logger.warn(markers, "EnrollmentVerifyDuplicationHandler")
            }
            return SQSHandlerResponse(shouldDeleteMessage)
        }

        runBlocking {
            val faceMapBytes = completedLiveness.faceMap!!.bytes
            val duplicationResult = faceMapSearch.search3D(
                faceMap = faceMapBytes,
                clientId = completedLiveness.clientId,
            ).getOrHandle {
                throw it
            }
            markers.andAppend("duplicatedExternalIds", duplicationResult).andAppend("clientId", completedLiveness.clientId.value)
            val fraudResult = faceMapSearch.search3D(
                faceMap = faceMapBytes,
                group = FaceMatchGroup.FRAUD_USERS,
                clientId = completedLiveness.clientId,
            ).getOrHandle {
                throw it
            }
            markers.andAppend("fraudResult", fraudResult)

            faceMapSearch.saveFaceMap(
                externalId = completedLiveness.externalId,
                faceMap = faceMapBytes,
                clientId = completedLiveness.clientId,
            )

            val duplicationVerifiedEvent = DuplicationVerified(
                livenessId = completedLiveness.id,
                duplications = duplicationResult.toSet(),
            )

            livenessEventRepository.save(duplicationVerifiedEvent)

            val fraudVerifiedEvent = FraudVerified(
                livenessId = completedLiveness.id,
                userList = fraudResult.toSet(),
            )
            livenessEventRepository.save(fraudVerifiedEvent)

            duplicationResult.forEach {
                livenessEventRepository.findEnrollmentByExternalId(it)?.let { conflictedLiveness ->
                    val event = DuplicationVerified(
                        livenessId = conflictedLiveness.id,
                        duplications = setOf(completedLiveness.externalId),
                    )

                    livenessEventRepository.save(event)
                }
            }
        }

        logger.info(markers, "EnrollmentVerifyDuplicationHandler")
        return SQSHandlerResponse(true)
    }

    @OptIn(ExperimentalContracts::class)
    private fun checkIsCompletedLiveness(liveness: Liveness?, markers: LogstashMarker): Either<Boolean, CompletedLiveness> {
        if (liveness == null) {
            markers.andAppend("context", "Enrollment Liveness not found")
            return true.left()
        }

        if (liveness.type != LivenessType.ENROLLMENT) {
            markers.andAppend("context", "Liveness found is not an enrollment")
            return false.left()
        }

        if (!isCompletedWithSuccess(liveness)) {
            markers.andAppend("context", "Enrollment Liveness is not completed")
            return true.left()
        }

        return liveness.right()
    }

    override fun handleError(m: Message, e: Exception): SQSHandlerResponse {
        val livenessId = try {
            parseObjectFrom<LivenessId>(m.body())
        } catch (e: Exception) {
            null
        }
        val markers = m.markers().andAppend("livenessId", livenessId?.value)
        logger.error(markers, "EnrollmentVerifyDuplicationHandler", e)
        return SQSHandlerResponse(false)
    }
}