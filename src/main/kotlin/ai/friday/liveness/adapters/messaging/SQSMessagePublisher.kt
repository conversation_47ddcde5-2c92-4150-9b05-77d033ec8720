package ai.friday.liveness.adapters.messaging

import ai.friday.liveness.adapters.parser.getObjectMapper
import ai.friday.liveness.andAppend
import ai.friday.liveness.app.integrations.MessagePublisher
import jakarta.inject.Singleton
import java.util.UUID
import net.logstash.logback.marker.Markers.append
import org.slf4j.LoggerFactory
import software.amazon.awssdk.services.sqs.SqsClient
import software.amazon.awssdk.services.sqs.model.GetQueueUrlRequest
import software.amazon.awssdk.services.sqs.model.SendMessageRequest

@Singleton
class SQSMessagePublisher(private val amazonSQS: SqsClient) : MessagePublisher {

    private val queueUrls = mutableMapOf<String, String>()

    private fun getQueueURL(queueName: String): String {
        return queueUrls.getOrPut(queueName) {
            amazonSQS.getQueueUrl(GetQueueUrlRequest.builder().queueName(queueName).build()).queueUrl()
        }
    }

    override fun sendMessage(queue: String, body: Any, delay: Int?) =
        sendMessage(QueueMessage(queueName = queue, getObjectMapper().writeValueAsString(body), delay))

    private fun sendMessage(queueMessage: QueueMessage) {
        val queueUrl = getQueueURL(queueMessage.queueName)

        val sendMessageRequestBuilder =
            SendMessageRequest.builder().queueUrl(queueUrl).messageBody(queueMessage.jsonObject)

        queueMessage.delaySeconds?.let { delaySeconds -> sendMessageRequestBuilder.delaySeconds(delaySeconds) }

        val sendMessageRequest = sendMessageRequestBuilder.build()

        val markers = append("queueMessage", queueMessage)
            .andAppend("messageRequest", sendMessageRequest)

        return try {
            val response = amazonSQS.sendMessage(sendMessageRequest)
            LOG.info(markers.andAppend("messageId", response.messageId()), "SQSMessagePublisher")
        } catch (ex: Exception) {
            LOG.error(markers, "SQSMessagePublisher", ex)
            throw ex
        }
    }

    companion object {
        private val LOG = LoggerFactory.getLogger(SQSMessagePublisher::class.java)
        private const val BATCH_SIZE = 10
    }
}

private data class QueueMessage(
    val queueName: String,
    val jsonObject: String,
    val delaySeconds: Int? = null,
    val id: String = UUID.randomUUID().toString(),
)