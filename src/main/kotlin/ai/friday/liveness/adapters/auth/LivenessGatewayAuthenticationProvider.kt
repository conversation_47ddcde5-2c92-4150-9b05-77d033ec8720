package ai.friday.liveness.adapters.auth

import ai.friday.liveness.app.auth.AuthConfig
import ai.friday.liveness.app.liveness.Role
import io.micronaut.http.HttpRequest
import io.micronaut.security.authentication.AuthenticationProvider
import io.micronaut.security.authentication.AuthenticationRequest
import io.micronaut.security.authentication.AuthenticationResponse
import io.reactivex.Flowable
import jakarta.inject.Singleton
import org.reactivestreams.Publisher

@Singleton
class LivenessGatewayAuthenticationProvider(
    private val authConfig: AuthConfig,
) : AuthenticationProvider {
    override fun authenticate(
        httpRequest: HttpRequest<*>?,
        authenticationRequest: AuthenticationRequest<*, *>,
    ): Publisher<AuthenticationResponse> {
        authConfig.tenants.forEach { (_, value) ->
            if (authenticationRequest.identity == value.main.username && authenticationRequest.secret == value.main.password) {
                return Flowable.just(AuthenticationResponse.success(value.main.username))
            }

            if (authenticationRequest.identity == value.backoffice.username && authenticationRequest.secret == value.backoffice.password) {
                return Flowable.just(AuthenticationResponse.success(value.backoffice.username, listOf(Role.BACKOFFICE.name)))
            }
        }

        return Flowable.just(AuthenticationResponse.failure())
    }
}