package ai.friday.liveness.adapters.api

import io.micronaut.context.annotation.Requires
import io.micronaut.context.event.ApplicationEventListener
import io.micronaut.discovery.event.ServiceReadyEvent
import io.micronaut.health.HealthStatus
import io.micronaut.management.health.indicator.HealthIndicator
import io.micronaut.management.health.indicator.HealthResult
import io.reactivex.Flowable
import jakarta.inject.Singleton
import java.io.File
import net.logstash.logback.marker.Markers.append
import org.reactivestreams.Publisher
import org.slf4j.LoggerFactory

@Singleton
@Requires(notEnv = ["test", "staging"])
class CustomHealthIndicator : ApplicationEventListener<Any>, HealthIndicator {
    private var isActive = false
    private val logger = LoggerFactory.getLogger(CustomHealthIndicator::class.java)

    override fun getResult(): Publisher<HealthResult> {
        val healthStatus = if (isActive) HealthStatus.UP else HealthStatus.DOWN
        return Flowable.just(HealthResult.builder(this::class.simpleName, healthStatus).build())
    }

    override fun onApplicationEvent(event: Any) {
        when (event) {
            is ServiceReadyEvent -> {
                try {
                    val files = File("/search3d3d").listFiles()

                    logger.info(append("filesSize", files?.size), "CustomHealthIndicatorOnServiceReadyEvent")

                    files?.forEach {
                        logger.info(append("file", it.name), "CustomHealthIndicatorOnServiceReadyEvent")
                    }

                    isActive = true
                } catch (e: Exception) {
                    logger.error(append("status", "error"), "CustomHealthIndicatorOnServiceReadyEvent", e)
                    throw e
                }
            }
        }
    }
}