package ai.friday.liveness.adapters.api

import ai.friday.liveness.app.liveness.LivenessService
import io.micronaut.scheduling.annotation.Scheduled
import jakarta.inject.Singleton
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

@Singleton
open class WarmUpJob(private val livenessService: LivenessService) {

    @Scheduled(fixedDelay = "12h")
    open fun warmUp() {
        logger.info(Markers.append("status", "fazendo o refresh do bean"), "warmUp")
    }

    companion object {
        private val logger = LoggerFactory.getLogger(WarmUpJob::class.java)
    }
}