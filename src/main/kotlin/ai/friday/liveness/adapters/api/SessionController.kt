package ai.friday.liveness.adapters.api

import ai.friday.liveness.adapters.http.genericHttpErrorResponse
import ai.friday.liveness.andAppend
import ai.friday.liveness.app.errors.GeneralError
import ai.friday.liveness.app.errors.SessionError
import ai.friday.liveness.app.session.OperationType
import ai.friday.liveness.app.session.SessionService
import ai.friday.liveness.app.session.SessionTokenRequest
import ai.friday.liveness.getClientIpAddresses
import arrow.core.getOrHandle
import io.micronaut.core.version.annotation.Version
import io.micronaut.http.HttpRequest
import io.micronaut.http.HttpResponse
import io.micronaut.http.annotation.Body
import io.micronaut.http.annotation.Controller
import io.micronaut.http.annotation.Header
import io.micronaut.http.annotation.Post
import io.micronaut.security.annotation.Secured
import io.micronaut.security.rules.SecurityRule
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

@Controller("/")
@Secured(SecurityRule.IS_ANONYMOUS)
class SessionController(
    private val sessionService: SessionService,
) {
    @Post("/session")
    suspend fun legacySession(
        @Header("X-Device-Key") deviceKeyIdentifier: String,
        @Header("X-User-Agent") userAgent: String,
        @Body sessionTO: LivenessSessionTO,
        httpRequest: HttpRequest<*>,
    ): HttpResponse<*> {
        return session(
            deviceKeyIdentifier = deviceKeyIdentifier,
            userAgent = userAgent,
            sessionTO = SessionTO(
                operationId = sessionTO.livenessId,
                type = OperationType.LIVENESS_CHECK,
            ),
            httpRequest = httpRequest,
        )
    }

    @Version("2")
    @Post("/session")
    suspend fun session(
        @Header("X-Device-Key") deviceKeyIdentifier: String,
        @Header("X-User-Agent") userAgent: String,
        @Body sessionTO: SessionTO,
        httpRequest: HttpRequest<*>,
    ): HttpResponse<*> {
        val request = SessionTokenRequest(
            operationId = sessionTO.operationId,
            type = sessionTO.type,
            deviceKeyIdentifier = deviceKeyIdentifier,
            userAgent = userAgent,
            clientIpAddress = getClientIpAddresses(httpRequest).joinToString(separator = ","),
        )
        val markers = Markers.append("request", request)
            .andAppend("xApiVersion", httpRequest.headers["X-API-VERSION"])
        logger.info(markers, "SessionController#session")

        val result = sessionService.createSession(request).getOrHandle {
            return when (it) {
                SessionError.NotProcessable, GeneralError.InvalidOperationId -> {
                    logger.error(markers.andAppend("operationError", it), "SessionController#session")
                    HttpResponse.unauthorized<Nothing>()
                }

                is GeneralError.ServerError -> {
                    logger.error(markers.andAppend("errorMessage", it.message), "SessionController#session", it.cause)
                    genericHttpErrorResponse
                }
            }
        }

        return HttpResponse.ok(
            mapOf(
                "token" to result.token,
            ),
        )
    }
    private val logger = LoggerFactory.getLogger(SessionController::class.java)
}

data class LivenessSessionTO(
    val livenessId: String,
)

data class SessionTO(
    val operationId: String,
    val type: OperationType,
)