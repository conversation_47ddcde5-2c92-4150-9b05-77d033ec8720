package ai.friday.liveness.adapters.api

import ai.friday.liveness.app.liveness.AgeEstimateGroup
import ai.friday.liveness.app.liveness.CompletedLiveness
import ai.friday.liveness.app.liveness.ExternalId
import ai.friday.liveness.app.liveness.Liveness
import ai.friday.liveness.app.liveness.LivenessEventRepository
import ai.friday.liveness.app.liveness.LivenessId
import ai.friday.liveness.app.liveness.LivenessStatus
import ai.friday.liveness.app.liveness.LivenessType
import ai.friday.liveness.app.liveness.PendingFaceVerification
import ai.friday.liveness.app.liveness.PendingSessionCreation
import ai.friday.liveness.app.liveness.PlatformType
import ai.friday.liveness.app.liveness.Role
import ai.friday.liveness.app.liveness.VerificationResult
import ai.friday.liveness.dateTimeFormat
import io.micronaut.http.HttpResponse
import io.micronaut.http.annotation.Controller
import io.micronaut.http.annotation.Get
import io.micronaut.http.annotation.PathVariable
import io.micronaut.security.annotation.Secured
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

@Secured(Role.Code.BACKOFFICE)
@Controller("/backoffice")
class BackofficeController(private val livenessEventRepository: LivenessEventRepository) {
    private val loggerName = "BackofficeController"

    @Get("/livenessId/{livenessId}")
    suspend fun getLivenessById(@PathVariable livenessId: String): HttpResponse<*> {
        val liveness = livenessEventRepository.findByIdOrNull(LivenessId(livenessId)) ?: return HttpResponse.notFound<Unit>()
        logger.info(Markers.append("livenessId", livenessId), loggerName)
        return HttpResponse.ok(liveness.toLivenessTO())
    }

    @Get("/enrollment/{externalId}")
    suspend fun getEnrollmentByExternalId(@PathVariable externalId: String): HttpResponse<*> {
        val liveness = livenessEventRepository.findEnrollmentByExternalId(ExternalId(externalId))
            ?: return HttpResponse.notFound<Unit>()
        logger.info(Markers.append("externalId", externalId), loggerName)
        return HttpResponse.ok(liveness.toLivenessTO())
    }

    @Get("/match/{externalId}")
    suspend fun getMatchByExternalId(@PathVariable externalId: String): HttpResponse<*> {
        val livenessList = livenessEventRepository.findMatchByExternalId(ExternalId(externalId))
        logger.info(Markers.append("externalId", externalId), loggerName)
        return HttpResponse.ok(livenessList.map { it.toLivenessTO() })
    }

    companion object {
        private val logger = LoggerFactory.getLogger(BackofficeController::class.java)
    }
}

private fun Liveness.toLivenessTO(): LivenessTO {
    val livenessTO = LivenessTO(
        id = this.id.value,
        externalId = this.externalId.value,
        type = this.type,
        createdAt = this.createdAt.format(dateTimeFormat),
        updatedAt = this.updatedAt.format(dateTimeFormat),
        status = this.status,
    )

    return when (this) {
        is PendingSessionCreation -> livenessTO
        is PendingFaceVerification -> livenessTO.copy(
            deviceKeyIdentifier = deviceKeyIdentifier,
            userAgent = userAgent,
            clientIpAddress = clientIpAddress,
            attempt = attempt,
        )

        is CompletedLiveness -> livenessTO.copy(
            deviceKeyIdentifier = deviceKeyIdentifier,
            userAgent = userAgent,
            clientIpAddress = clientIpAddress,
            attempt = attempt,

            ageEstimateGroup = ageEstimateGroup,
            platformType = platformType,
            deviceSDKVersion = deviceSDKVersion,
            result = result,
            duplications = duplications?.map { it.value },
            fraudIndications = fraudIndications?.map { it.value },
            markedAsFraud = markedAsFraud,
        )
    }
}

private data class LivenessTO(
    val id: String,
    val externalId: String,
    val type: LivenessType,
    val createdAt: String,
    val updatedAt: String,
    val status: LivenessStatus,

    val deviceKeyIdentifier: String? = null,
    val userAgent: String? = null,
    val clientIpAddress: String? = null,
    val attempt: Int? = null,

    val ageEstimateGroup: AgeEstimateGroup? = null,
    val platformType: PlatformType? = null,
    val deviceSDKVersion: String? = null,
    val result: VerificationResult? = null,
    val duplications: List<String>? = null,
    val fraudIndications: List<String>? = null,
    val markedAsFraud: Boolean? = null,
)