package ai.friday.liveness.adapters.api

import ai.friday.liveness.adapters.http.genericHttpErrorResponse
import ai.friday.liveness.andAppend
import ai.friday.liveness.app.errors.GeneralError
import ai.friday.liveness.app.errors.LivenessError
import ai.friday.liveness.app.liveness.LivenessCheckRequest
import ai.friday.liveness.app.liveness.LivenessCheckResult
import ai.friday.liveness.app.liveness.LivenessId
import ai.friday.liveness.app.liveness.LivenessService
import ai.friday.liveness.getClientIpAddresses
import ai.friday.liveness.measure
import arrow.core.getOrHandle
import io.micronaut.http.HttpMethod
import io.micronaut.http.HttpRequest
import io.micronaut.http.HttpResponse
import io.micronaut.http.annotation.Body
import io.micronaut.http.annotation.Controller
import io.micronaut.http.annotation.Header
import io.micronaut.http.annotation.Post
import io.micronaut.security.annotation.Secured
import io.micronaut.security.rules.SecurityRule
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

@Controller("/")
@Secured(SecurityRule.IS_ANONYMOUS)
class LivenessController(
    private val livenessService: LivenessService,
    private val stressTestDataCollector: StressTestDataCollector?,
) {

    @Post("/check")
    suspend fun check(
        @Header("X-Device-Key") deviceKeyIdentifier: String,
        @Header("X-User-Agent") userAgent: String,
        @Body checkRequestTO: CheckRequestTO,
        httpRequest: HttpRequest<*>,
    ): HttpResponse<*> {
        stressTestDataCollector?.collect(
            method = HttpMethod.POST,
            path = "/check",
            operationId = checkRequestTO.livenessId,
            headers = mapOf(
                "X-Device-Key" to deviceKeyIdentifier,
                "X-User-Agent" to userAgent,
            ),
            body = checkRequestTO,
        )

        val request = checkRequestTO.toLivenessCheckRequest(
            deviceKeyIdentifier = deviceKeyIdentifier,
            userAgent = userAgent,
            clientIpAddress = getClientIpAddresses(httpRequest).joinToString(separator = ","),
        )

        val markers = Markers.append("request", request.toLogParameters())

        val (result, timeElapsed) =
            measure {
                livenessService.check(request)
                    .getOrHandle {
                        return when (it) {
                            GeneralError.InvalidOperationId, LivenessError.AlreadyProcessed, LivenessError.LivenessNotFound, LivenessError.DuplicatedExternalId, LivenessError.InvalidLivenessState, LivenessError.FaceScanReplayed -> {
                                logger.error(markers.andAppend("livenessError", it), "LivenessController#check")
                                HttpResponse.unauthorized<Nothing>()
                            }

                            is GeneralError.ServerError -> {
                                logger.error(
                                    markers.andAppend("errorMessage", it.message),
                                    "LivenessController#check",
                                    it.cause,
                                )
                                genericHttpErrorResponse
                            }
                        }
                    }
            }

        markers.andAppend("timeElapsed", timeElapsed)

        val (succeeded, scanResultBlob) = when (result) {
            is LivenessCheckResult.Failed -> Pair(false, result.scanResultBlob)
            is LivenessCheckResult.Success -> Pair(true, result.scanResultBlob)
        }

        logger.info(markers.andAppend("succeeded", succeeded), "LivenessController#check")

        return HttpResponse.ok(
            mapOf(
                "succeeded" to succeeded,
                "scanResultBlob" to scanResultBlob,
            ),
        )
    }

    private fun LivenessCheckRequest.toLogParameters(): Map<String, Any> {
        return mapOf(
            "faceScanSize" to faceScan.length,
            "auditTrailImageSize" to auditTrailImage.length,
            "lowQualityAuditTrailImageSize" to (lowQualityAuditTrailImage?.length ?: 0),
            "userAgent" to userAgent,
            "deviceKeyIdentifier" to deviceKeyIdentifier,
            "clientIpAddress" to clientIpAddress,
            "livenessId" to livenessId.value,
        )
    }

    private val logger = LoggerFactory.getLogger(LivenessController::class.java)
}

data class CheckRequestTO(
    val faceScan: String,
    val auditTrailImage: String,
    val lowQualityAuditTrailImage: String?,
    val livenessId: String,
)

private fun CheckRequestTO.toLivenessCheckRequest(
    deviceKeyIdentifier: String,
    userAgent: String,
    clientIpAddress: String,
) =
    LivenessCheckRequest(
        faceScan = faceScan,
        auditTrailImage = auditTrailImage,
        lowQualityAuditTrailImage = lowQualityAuditTrailImage,
        livenessId = LivenessId(livenessId),
        deviceKeyIdentifier = deviceKeyIdentifier,
        userAgent = userAgent,
        clientIpAddress = clientIpAddress,
    )