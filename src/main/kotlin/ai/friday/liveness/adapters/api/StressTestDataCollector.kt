package ai.friday.liveness.adapters.api

import ai.friday.liveness.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.liveness.adapters.parser.getObjectMapper
import ai.friday.liveness.andAppend
import io.micronaut.context.annotation.Requires
import io.micronaut.http.HttpMethod
import io.micronaut.objectstorage.aws.AwsS3Operations
import io.micronaut.objectstorage.request.UploadRequest
import jakarta.inject.Singleton
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

@Singleton
@Requires(property = "features.enable-data-collection", value = "true")
class StressTestDataCollector(
    private val objectStorage: AwsS3Operations,
) {

    private val logger = LoggerFactory.getLogger(StressTestDataCollector::class.java)

    private val objectMapper = getObjectMapper()

    fun collect(method: HttpMethod, path: String, operationId: String, headers: Map<String, String>?, body: Any?) {
        val markers = Markers.append("method", method)
            .andAppend("path", path)
            .andAppend("operationId", operationId)
        try {
            val created = getZonedDateTime()
            val createdInstant = created.toInstant()

            val key = "STD/${method}_${path.replace('/','_')}_${createdInstant.epochSecond}_${createdInstant.nano.toString().padStart(9,'0')}.json"
            markers.andAppend("key", key)

            val uploadRequest = UploadRequest.fromBytes(
                objectMapper.writeValueAsBytes(
                    StressTestData(
                        headers = headers,
                        body = body,
                    ),
                ),
                key,
            )
            uploadRequest.setContentType("application/json")

            val response = objectStorage.upload(uploadRequest)

            with(response.nativeResponse.sdkHttpResponse()) {
                markers.andAppend("statusCode", statusCode())
                with(statusText()) {
                    if (isPresent) {
                        markers.andAppend("statusText", get())
                    }
                }

                if (isSuccessful) {
                    logger.info(markers, "StressTestDataCollector#collect")
                } else {
                    logger.warn(markers, "StressTestDataCollector#collect")
                }
            }
        } catch (e: Exception) {
            logger.error(markers, "StressTestDataCollector#collect", e)
        }
    }
}

data class StressTestData(
    val headers: Map<String, String>? = null,
    var body: Any? = null,
)