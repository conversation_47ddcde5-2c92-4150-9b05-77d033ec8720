package ai.friday.liveness.adapters.api

import io.micronaut.context.annotation.ConfigurationProperties
import io.micronaut.http.HttpResponse
import io.micronaut.http.annotation.Controller
import io.micronaut.http.annotation.Get
import io.micronaut.security.annotation.Secured
import io.micronaut.security.rules.SecurityRule

@ConfigurationProperties("integrations.facetec.client")
open class ClientFaceTecSDKConfig {
    var productionKeyText: String? = null
    lateinit var deviceKeyIdentifier: String
    lateinit var publicFaceScanEncryptionKey: String
}

@Controller("/")
@Secured(SecurityRule.IS_ANONYMOUS)
class ConfigurationController(
    private val clientConfig: ClientFaceTecSDKConfig,
) {

    @Get("/config")
    fun getConfiguration(): HttpResponse<*> {
        return HttpResponse.ok(
            ConfigTO(
                deviceKeyIdentifier = clientConfig.deviceKeyIdentifier,
                productionKeyText = clientConfig.productionKeyText,
                publicFaceScanEncryptionKey = clientConfig.publicFaceScanEncryptionKey,
            ),
        )
    }
}

data class ConfigTO(
    val deviceKeyIdentifier: String,
    val productionKeyText: String?,
    val publicFaceScanEncryptionKey: String,
)