package ai.friday.liveness.adapters.api

import ai.friday.liveness.ByteWrapper
import ai.friday.liveness.adapters.api.CreateLivenessErrorMessages.INVALID_EXTERNAL_ID
import ai.friday.liveness.adapters.api.CreateLivenessErrorMessages.INVALID_LIVENESS_ID
import ai.friday.liveness.adapters.http.HttpErrorResponse
import ai.friday.liveness.adapters.http.asBadRequest
import ai.friday.liveness.adapters.http.asConflict
import ai.friday.liveness.adapters.http.genericHttpErrorResponse
import ai.friday.liveness.andAppend
import ai.friday.liveness.app.errors.GeneralError
import ai.friday.liveness.app.errors.LivenessError
import ai.friday.liveness.app.liveness.ExternalId
import ai.friday.liveness.app.liveness.LivenessId
import ai.friday.liveness.app.liveness.LivenessProvider
import ai.friday.liveness.app.liveness.LivenessService
import ai.friday.liveness.app.liveness.SaveLivenessError
import ai.friday.liveness.app.session.LivenessRequest
import arrow.core.getOrHandle
import com.fasterxml.jackson.annotation.JsonInclude
import io.micronaut.http.HttpResponse
import io.micronaut.http.HttpResponse.badRequest
import io.micronaut.http.HttpResponse.created
import io.micronaut.http.HttpResponse.noContent
import io.micronaut.http.HttpResponse.notFound
import io.micronaut.http.HttpResponse.ok
import io.micronaut.http.annotation.Body
import io.micronaut.http.annotation.Controller
import io.micronaut.http.annotation.Get
import io.micronaut.http.annotation.PathVariable
import io.micronaut.http.annotation.Post
import io.micronaut.security.annotation.Secured
import io.micronaut.security.authentication.Authentication
import io.micronaut.security.rules.SecurityRule
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

@Controller("/internal")
@Secured(SecurityRule.IS_AUTHENTICATED)
class InternalLivenessController(
    private val livenessService: LivenessService,
) {

    @Post("/enrollment")
    suspend fun createEnrollment(
        @Body body: CreateLivenessTO,
        authentication: Authentication,
    ): HttpResponse<*> {
        val marker = Markers.append("request", body).andAppend("authentication", authentication.name)
        val logMessage = "InternalLivenessController#createLivenesss"

        val provider = body.provider?.let { providerValue ->
            LivenessProvider.values().find { it.value == providerValue } ?: LivenessProvider.FACETECH
        } ?: LivenessProvider.FACETECH

        val enrollmentRequest =
            LivenessRequest(externalId = ExternalId(body.externalId), clientId = authentication.name, provider = provider)

        val liveness = livenessService.createEnrollment(enrollmentRequest)
            .getOrHandle {
                when (it) {
                    SaveLivenessError.InvalidExternalId -> return INVALID_EXTERNAL_ID.asBadRequest()
                    SaveLivenessError.AlreadyEnrolled -> return INVALID_EXTERNAL_ID.asConflict()
                    is SaveLivenessError.Generic -> {
                        logger.error(marker, logMessage, it.exception)
                        return genericHttpErrorResponse
                    }
                }
            }

        logger.info(marker.andAppend("liveness", liveness), logMessage)

        return created(CreateLivenessResponseTO(livenessId = liveness.id.value))
    }

    @Get("/enrollment/hasCompletedEnrollment/{externalId}")
    suspend fun hasCompletedEnrollment(
        @PathVariable externalId: String,
        authentication: Authentication,
    ): HttpResponse<*> {
        val marker = Markers.append("externalId", externalId).andAppend("authentication", authentication.name)
        val logMessage = "InternalLivenessController#hasCompletedEnrollment"

        val enrollmentRequest =
            LivenessRequest(externalId = ExternalId(externalId), clientId = authentication.name)

        val hasCompletedEnrollment = livenessService.hasCompletedEnrollment(enrollmentRequest)
            .getOrHandle {
                when (it) {
                    is SaveLivenessError.Generic -> {
                        logger.error(marker, logMessage, it.exception)
                        return genericHttpErrorResponse
                    }

                    else -> {
                        logger.error(marker, logMessage, IllegalStateException(it.javaClass.simpleName))
                        return genericHttpErrorResponse
                    }
                }
            }

        logger.info(marker.andAppend("hasCompletedEnrollment", hasCompletedEnrollment), logMessage)

        return ok(CheckEnrollmentResponseTO(hasCompletedLiveness = hasCompletedEnrollment))
    }

    @Post("/fraud")
    suspend fun createFraud(
        @Body body: CreateLivenessTO,
        authentication: Authentication,
    ): HttpResponse<*> {
        val marker = Markers.append("request", body).andAppend("authentication", authentication.name)
        val logMessage = "InternalLivenessController#createFraud"

        livenessService.createFraud(ExternalId(body.externalId)).getOrHandle {
            marker.andAppend("LivenessError", it)
            logger.error(marker, logMessage)
            when (it) {
                LivenessError.InvalidLivenessState -> return badRequest("Liveness not completed")
                LivenessError.LivenessNotFound -> return notFound("Liveness not found")
                else -> {
                    return genericHttpErrorResponse
                }
            }
        }

        logger.info(marker, logMessage)

        return noContent<Unit>()
    }

    @Post("/match")
    suspend fun createMatch(
        @Body body: CreateLivenessTO,
        authentication: Authentication,
    ): HttpResponse<*> {
        val marker = Markers.append("request", body).andAppend("authentication", authentication.name)
        val logMessage = "InternalLivenessController#createMatch"

        val provider = body.provider?.let { providerValue ->
            LivenessProvider.values().find { it.value == providerValue } ?: LivenessProvider.FACETECH
        } ?: LivenessProvider.FACETECH

        val matchRequest = LivenessRequest(externalId = ExternalId(body.externalId), clientId = authentication.name, provider = provider)

        val match = livenessService.createMatch(matchRequest)
            .getOrHandle {
                when (it) {
                    SaveLivenessError.InvalidExternalId -> return INVALID_EXTERNAL_ID.asBadRequest()
                    SaveLivenessError.AlreadyEnrolled -> {
                        logger.error(marker, logMessage)
                        return genericHttpErrorResponse
                    }

                    is SaveLivenessError.Generic -> {
                        logger.error(marker, logMessage, it.exception)
                        return genericHttpErrorResponse
                    }
                }
            }

        logger.info(marker.andAppend("match", match), logMessage)

        return created(CreateLivenessResponseTO(livenessId = match.id.value))
    }

    @Get("/enrollment/{livenessId}/auditImage")
    suspend fun auditImage(@PathVariable livenessId: String, authentication: Authentication): HttpResponse<*> {
        val logMessage = "InternalLivenessController#auditImage"
        val markers = Markers.append("livenessId", livenessId).andAppend("authentication", authentication.name)

        try {
            livenessService.retrieveAuditImage(LivenessId(livenessId)).map { imageBytes ->
                logger.info(markers.andAppend("size", imageBytes.size), logMessage)

                return ok(
                    AutidImageTO(
                        livenessId = livenessId,
                        auditImage = ByteWrapper(imageBytes).getBase64(),
                    ),
                )
            }.getOrHandle {
                when (it) {
                    GeneralError.InvalidOperationId -> {
                        logger.error(markers.andAppend("errorMessage", "InvalidLivenessId"), logMessage)
                        return INVALID_LIVENESS_ID.asBadRequest()
                    }

                    LivenessError.InvalidLivenessState -> {
                        logger.warn(markers.andAppend("errorMessage", "InvalidLivenessState"), logMessage)
                        return INVALID_LIVENESS_ID.asBadRequest()
                    }

                    is GeneralError.ServerError -> {
                        logger.error(markers.andAppend("errorMessage", it.message), logMessage, it.cause)
                        return genericHttpErrorResponse
                    }

                    else -> {
                        logger.error(markers.andAppend("invalidState", it.javaClass.simpleName), logMessage)
                        return genericHttpErrorResponse
                    }
                }
            }
        } catch (e: IllegalArgumentException) {
            logger.error(markers, logMessage, e)
            return INVALID_LIVENESS_ID.asBadRequest()
        }
    }

    @Get("/enrollmentVerification/{externalId}")
    suspend fun enrollmentVerification(
        @PathVariable externalId: String,
        authentication: Authentication,
    ): HttpResponse<*> {
        val logMessage = "InternalLivenessController#enrollmentVerification"
        val marker = Markers.append("externalId", externalId).andAppend("authentication", authentication.name)
        val result = livenessService.enrollmentVerification(ExternalId(externalId)).getOrHandle {
            when (it) {
                LivenessError.AlreadyProcessed, GeneralError.InvalidOperationId, LivenessError.DuplicatedExternalId, LivenessError.InvalidLivenessState, LivenessError.FaceScanReplayed, LivenessError.LivenessNotFound -> {
                    logger.error(
                        marker.andAppend("reason", it),
                        logMessage,
                    )
                    return INVALID_EXTERNAL_ID.asBadRequest()
                }

                is GeneralError.ServerError -> {
                    logger.error(
                        marker,
                        logMessage,
                        it.cause,
                    )
                    return genericHttpErrorResponse
                }
            }
        }

        logger.info(
            marker.andAppend("duplications", result.duplications)
                .andAppend("fraudIndications", result.fraudIndications),
            logMessage,
        )

        return ok(
            EnrollmentVerificationTO(
                duplications = result.duplications?.map { it.value },
                fraudIndications = result.fraudIndications?.map { it.value },
            ),
        )
    }

    @Deprecated("Use /verify/{externalId instead")
    @Get("/duplicationVerification/{externalId}")
    suspend fun verifyDuplication(
        @PathVariable externalId: String,
        authentication: Authentication,
    ): HttpResponse<*> {
        val logMessage = "InternalLivenessController#verifyDuplication"
        val marker = Markers.append("externalId", externalId).andAppend("authentication", authentication.name)
        val result = livenessService.getDuplicationVerification(ExternalId(externalId)).getOrHandle {
            return when (it) {
                LivenessError.AlreadyProcessed, GeneralError.InvalidOperationId, LivenessError.DuplicatedExternalId, LivenessError.InvalidLivenessState, LivenessError.FaceScanReplayed, LivenessError.LivenessNotFound -> {
                    logger.error(
                        marker.andAppend("reason", it),
                        logMessage,
                    )
                    INVALID_EXTERNAL_ID.asBadRequest()
                }

                is GeneralError.ServerError -> {
                    logger.error(
                        marker,
                        logMessage,
                        it.cause,
                    )
                    genericHttpErrorResponse
                }
            }
        }

        logger.info(marker.andAppend("duplications", result.duplications), logMessage)

        return ok(
            EnrollmentVerificationTO(
                duplications = result.duplications?.map { it.value },
                fraudIndications = emptyList(),
            ),
        )
    }

    @Get("/match/validated/{livenessId}")
    suspend fun matchVerification(
        @PathVariable livenessId: String,
        authentication: Authentication,
    ): HttpResponse<*> {
        val logMessage = "InternalLivenessController#matchVerification"
        val marker = Markers.append("livenessId", livenessId).andAppend("authentication", authentication.name)
        val result = livenessService.checkMatchCompleted(LivenessId(livenessId)).getOrHandle {
            return when (it) {
                GeneralError.InvalidOperationId -> {
                    logger.warn(marker.andAppend("invalidLivenessId", true), logMessage)
                    return badRequest<Unit>()
                }

                LivenessError.AlreadyProcessed, LivenessError.DuplicatedExternalId, LivenessError.InvalidLivenessState, LivenessError.FaceScanReplayed, LivenessError.LivenessNotFound -> {
                    logger.error(
                        marker.andAppend("reason", it),
                        logMessage,
                    )
                    genericHttpErrorResponse
                }

                is GeneralError.ServerError -> {
                    logger.error(
                        marker,
                        logMessage,
                        it.cause,
                    )
                    genericHttpErrorResponse
                }
            }
        }
        logger.info(marker.andAppend("checkMatchCompleted", result), logMessage)
        return ok(
            MatchVerificationResponseTO(
                livenessId = result.livenessId.value,
                externalId = result.externalId.value,
                match = result.hasMatch,
                attempt = result.attempt,
            ),
        )
    }

    private val logger = LoggerFactory.getLogger(InternalLivenessController::class.java)
}

data class AutidImageTO(
    val livenessId: String,
    val auditImage: String,
)

data class CreateLivenessTO(
    val externalId: String,
    val provider: String? = null,
)

data class CreateLivenessResponseTO(
    val livenessId: String,
)

@JsonInclude(JsonInclude.Include.NON_NULL)
data class EnrollmentVerificationTO(
    val duplications: List<String>?,
    val fraudIndications: List<String>?,
)

@JsonInclude(JsonInclude.Include.NON_NULL)
data class MatchVerificationResponseTO(
    val livenessId: String,
    val externalId: String,
    val match: Boolean,
    val attempt: Int,
)

@JsonInclude(JsonInclude.Include.NON_NULL)
data class CheckEnrollmentResponseTO(
    val hasCompletedLiveness: Boolean,
)

private enum class CreateLivenessErrorMessages(override val code: Int, override val message: String) :
    HttpErrorResponse {
    INVALID_EXTERNAL_ID(4002, "Invalid external id"),
    INVALID_LIVENESS_ID(4003, "Invalid liveness id"),
}