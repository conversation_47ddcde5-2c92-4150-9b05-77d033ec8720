package ai.friday.liveness.adapters.api

import ai.friday.liveness.adapters.http.genericHttpErrorResponse
import ai.friday.liveness.andAppend
import ai.friday.liveness.app.documentscan.DocumentScanId
import ai.friday.liveness.app.documentscan.DocumentScanRequest
import ai.friday.liveness.app.documentscan.DocumentScanResult
import ai.friday.liveness.app.documentscan.DocumentScanService
import ai.friday.liveness.app.errors.DocumentScanError
import ai.friday.liveness.app.errors.GeneralError
import ai.friday.liveness.getClientIpAddresses
import arrow.core.getOrHandle
import io.micronaut.http.HttpMethod
import io.micronaut.http.HttpRequest
import io.micronaut.http.HttpResponse
import io.micronaut.http.annotation.Body
import io.micronaut.http.annotation.Controller
import io.micronaut.http.annotation.Header
import io.micronaut.http.annotation.Post
import io.micronaut.security.annotation.Secured
import io.micronaut.security.rules.SecurityRule
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

@Controller("/")
@Secured(SecurityRule.IS_ANONYMOUS)
class DocumentScanController(
    private val documentScanService: DocumentScanService,
    private val stressTestDataCollector: StressTestDataCollector?,
) {
    @Post("/document-scan")
    suspend fun documentScan(
        @Header("X-Device-Key") deviceKeyIdentifier: String,
        @Header("X-User-Agent") userAgent: String,
        @Body documentScanRequestTO: DocumentScanRequestTO,
        httpRequest: HttpRequest<*>,
    ): HttpResponse<*> {
        stressTestDataCollector?.collect(
            method = HttpMethod.POST,
            path = "/document-scan",
            operationId = documentScanRequestTO.operationId,
            headers = mapOf(
                "X-Device-Key" to deviceKeyIdentifier,
                "X-User-Agent" to userAgent,
            ),
            body = documentScanRequestTO,
        )

        val request = documentScanRequestTO.toDocumentScanCheckRequest(
            deviceKeyIdentifier = deviceKeyIdentifier,
            userAgent = userAgent,
            clientIpAddress = getClientIpAddresses(httpRequest).joinToString(separator = ","),
        )

        val markers = Markers.append("request", request.toLogParameters())

        val result = documentScanService.scan(request).getOrHandle {
            return when (it) {
                DocumentScanError.DocumentScanReplayed, DocumentScanError.InvalidState, DocumentScanError.AlreadyProcessed, GeneralError.InvalidOperationId -> {
                    logger.error(markers.andAppend("documentScanError", it.toString()), "DocumentScanController#scan")
                    HttpResponse.unauthorized<Nothing>()
                }

                is GeneralError.ServerError -> {
                    logger.error(
                        markers.andAppend("errorMessage", it.message),
                        "DocumentScanController#scan",
                        it.cause,
                    )
                    genericHttpErrorResponse
                }
            }
        }

        val (succeeded, scanResultBlob) = when (result) {
            is DocumentScanResult.Failed -> Pair(false, result.blob)
            is DocumentScanResult.Success -> Pair(true, result.blob)
        }

        logger.info(markers.andAppend("succeeded", succeeded), "DocumentScanController#scan")

        return HttpResponse.ok(
            mapOf(
                "succeeded" to succeeded,
                "scanResultBlob" to scanResultBlob,
            ),
        )
    }

    private val logger = LoggerFactory.getLogger(DocumentScanController::class.java)
}

data class DocumentScanRequestTO(
    val operationId: String,
    val idScan: String,
    val frontImage: String?,
    val backImage: String?,
)

private fun DocumentScanRequest.toLogParameters(): Map<String, Any> {
    return mapOf(
        "documentScanId" to id.value,
        "deviceKeyIdentifier" to deviceKeyIdentifier,
        "userAgent" to userAgent,
        "clientIpAddress" to clientIpAddress,
        "frontImageSize" to (frontImage?.length ?: 0),
        "backImageSize" to (backImage?.length ?: 0),
        "idScanSize" to (idScan.length),
    )
}

private fun DocumentScanRequestTO.toDocumentScanCheckRequest(
    deviceKeyIdentifier: String,
    userAgent: String,
    clientIpAddress: String,
): DocumentScanRequest {
    return DocumentScanRequest(
        id = DocumentScanId(operationId),
        idScan = idScan,
        frontImage = frontImage,
        backImage = backImage,
        userAgent = userAgent,
        clientIpAddress = clientIpAddress,
        deviceKeyIdentifier = deviceKeyIdentifier,
    )
}