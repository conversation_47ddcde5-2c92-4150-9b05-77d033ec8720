package ai.friday.liveness.adapters.api

import ai.friday.liveness.adapters.api.CreateDocumentScanErrorMessages.INVALID_EXTERNAL_ID
import ai.friday.liveness.adapters.http.HttpErrorResponse
import ai.friday.liveness.adapters.http.asBadRequest
import ai.friday.liveness.adapters.http.asConflict
import ai.friday.liveness.adapters.http.genericHttpErrorResponse
import ai.friday.liveness.andAppend
import ai.friday.liveness.app.documentscan.CreateDocumentScanRequest
import ai.friday.liveness.app.documentscan.DocumentGetDataError
import ai.friday.liveness.app.documentscan.DocumentGetImagesError
import ai.friday.liveness.app.documentscan.DocumentScanId
import ai.friday.liveness.app.documentscan.DocumentScanService
import ai.friday.liveness.app.errors.DocumentScanError
import ai.friday.liveness.app.errors.GeneralError
import ai.friday.liveness.app.liveness.ExternalId
import arrow.core.getOrHandle
import io.micronaut.http.HttpResponse
import io.micronaut.http.HttpResponse.created
import io.micronaut.http.HttpStatus
import io.micronaut.http.annotation.Body
import io.micronaut.http.annotation.Controller
import io.micronaut.http.annotation.Get
import io.micronaut.http.annotation.PathVariable
import io.micronaut.http.annotation.Post
import io.micronaut.security.annotation.Secured
import io.micronaut.security.authentication.Authentication
import io.micronaut.security.rules.SecurityRule
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

@Controller("/internal")
@Secured(SecurityRule.IS_AUTHENTICATED)
class InternalDocumentScanController(
    private val service: DocumentScanService,
) {

    @Post("/document-scan")
    suspend fun postDocumentScan(
        @Body body: CreateDocumentScanTO,
        authentication: Authentication,
    ): HttpResponse<*> {
        val marker = Markers.append("request", body).andAppend("authentication", authentication.name)
        val logMessage = "InternalDocumentScanController#documentScan"

        val documentScanRequest =
            CreateDocumentScanRequest(externalId = ExternalId(body.externalId), clientId = authentication.name)

        val result = service.create(documentScanRequest)
            .getOrHandle {
                when (it) {
                    GeneralError.InvalidOperationId -> return INVALID_EXTERNAL_ID.asBadRequest()
                    DocumentScanError.DocumentScanReplayed, DocumentScanError.InvalidState, DocumentScanError.AlreadyProcessed -> return INVALID_EXTERNAL_ID.asConflict()
                    is GeneralError.ServerError -> {
                        logger.error(marker, logMessage, it.cause)
                        return genericHttpErrorResponse
                    }
                }
            }

        logger.info(marker.andAppend("documentScan", result), logMessage)

        return created(CreateDocumentScanResponseTO(documentScanId = result.id.value))
    }

    @Get("/document-scan/{documentScanId}/images")
    suspend fun getImages(@PathVariable documentScanId: String): HttpResponse<*> {
        return service.getImages(DocumentScanId(documentScanId)).fold(
            ifLeft = {
                when (it) {
                    DocumentGetImagesError.NotFound -> HttpResponse.notFound()
                    DocumentGetImagesError.InvalidState -> HttpResponse.status(HttpStatus.CONFLICT)
                    is DocumentGetImagesError.Error -> HttpResponse.serverError()
                }
            },
            ifRight = { (frontImage, backImage) ->
                HttpResponse.ok(DocumentScanImageResponseTO(frontImage, backImage))
            },
        )
    }

    @Get("/document-scan/{documentScanId}")
    suspend fun getDocumentScanData(@PathVariable documentScanId: String): HttpResponse<*> {
        return service.getData(DocumentScanId(documentScanId)).fold(
            ifLeft = {
                when (it) {
                    DocumentGetDataError.NotFound -> HttpResponse.notFound()
                    DocumentGetDataError.InvalidState -> HttpResponse.status(HttpStatus.CONFLICT)
                }
            },
            ifRight = {
                HttpResponse.ok(
                    DocumentScanDataResponseTO(
                        spoof = it.digitalSpoofStatus.name,
                        face = it.faceOnDocument.name,
                        text = it.textOnDocument.name,
                        ocr = !it.didCompleteIDScanWithoutMatchingOCRTemplate,
                    ),
                )
            },
        )
    }

    private val logger = LoggerFactory.getLogger(InternalDocumentScanController::class.java)
}

data class DocumentScanImageResponseTO(
    val frontImage: String,
    val backImage: String?,
)

data class DocumentScanDataResponseTO(
    val spoof: String,
    val face: String,
    val text: String,
    val ocr: Boolean,
)

data class CreateDocumentScanResponseTO(
    val documentScanId: String,
)

data class CreateDocumentScanTO(
    val externalId: String,
)

private enum class CreateDocumentScanErrorMessages(override val code: Int, override val message: String) :
    HttpErrorResponse {
    INVALID_EXTERNAL_ID(4002, "Invalid external id"),
}