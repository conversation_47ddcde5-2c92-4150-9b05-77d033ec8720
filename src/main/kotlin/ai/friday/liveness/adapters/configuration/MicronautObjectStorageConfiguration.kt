package ai.friday.liveness.adapters.configuration

import ai.friday.liveness.app.documentscan.ObjectStorageConfiguration
import io.micronaut.context.annotation.ConfigurationInject
import io.micronaut.context.annotation.ConfigurationProperties

@ConfigurationProperties("facetec.s3")
class MicronautObjectStorageConfiguration @ConfigurationInject constructor(
    override val region: String,
    override val bucket: String,
) : ObjectStorageConfiguration