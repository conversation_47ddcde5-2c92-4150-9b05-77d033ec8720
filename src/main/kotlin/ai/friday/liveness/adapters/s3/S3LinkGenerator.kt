package ai.friday.liveness.adapters.s3

import ai.friday.liveness.app.documentscan.ObjectLinkGenerator
import io.micronaut.context.annotation.Factory
import jakarta.inject.Singleton
import kotlin.time.Duration.Companion.days
import kotlin.time.toJavaDuration
import software.amazon.awssdk.services.s3.presigner.S3Presigner
import software.amazon.awssdk.services.s3.presigner.model.GetObjectPresignRequest

@Factory
class S3PresignerFactory {
    @Singleton
    fun s3Presigner(): S3Presigner {
        return S3Presigner.create()
    }
}

@Singleton
class S3LinkGenerator(private val s3Presigner: S3Presigner) : ObjectLinkGenerator {

    override suspend fun getObjectPresigned(bucketName: String, keyName: String): String {
        val request = GetObjectPresignRequest
            .builder()
            .getObjectRequest { it.bucket(bucketName).key(keyName) }
            .signatureDuration(1.days.toJavaDuration())
            .build()

        val presignObject = s3Presigner.presignGetObject(request)
        return presignObject.url().toString()
    }
}