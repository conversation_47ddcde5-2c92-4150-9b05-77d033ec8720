package ai.friday.liveness.adapters.dynamo.documentscan

import ai.friday.liveness.adapters.dynamo.AbstractDynamoDAO
import ai.friday.liveness.adapters.dynamo.GlobalSecondaryIndexes
import ai.friday.liveness.adapters.dynamo.LIVENESS_TABLE_NAME
import ai.friday.liveness.adapters.parser.getObjectMapper
import ai.friday.liveness.adapters.parser.parseObjectFrom
import ai.friday.liveness.app.LivenessClientId
import ai.friday.liveness.app.documentscan.DocumentProcessed
import ai.friday.liveness.app.documentscan.DocumentScan
import ai.friday.liveness.app.documentscan.DocumentScanCompleted
import ai.friday.liveness.app.documentscan.DocumentScanCreated
import ai.friday.liveness.app.documentscan.DocumentScanError
import ai.friday.liveness.app.documentscan.DocumentScanEvent
import ai.friday.liveness.app.documentscan.DocumentScanEventRepository
import ai.friday.liveness.app.documentscan.DocumentScanId
import ai.friday.liveness.app.documentscan.DocumentScanSessionCreated
import ai.friday.liveness.app.liveness.ExternalId
import jakarta.inject.Singleton
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedClient

private const val DOCUMENT_SCAN_DIGEST_PREFIX = "DOCUMENT_SCAN_DIGEST#"
private const val EXTERNAL_ID_PREFIX = "EXTERNAL_ID#"

private class ScanEventWrapperDynamoDAO(cli: DynamoDbEnhancedClient) :
    AbstractDynamoDAO<DocumentScanEventWrapperEntity>(cli) {
    override fun args() = LIVENESS_TABLE_NAME to DocumentScanEventWrapperEntity::class.java
}

@Singleton
class DocumentScanEventDynamoDbRepository(cli: DynamoDbEnhancedClient) : DocumentScanEventRepository {

    private val logger = LoggerFactory.getLogger(DocumentScanEventDynamoDbRepository::class.java)

    private val db = ScanEventWrapperDynamoDAO(cli)

    override suspend fun save(event: DocumentScanEvent) {
        logger.info(Markers.append("document", event), "DocumentScanEventDynamoDbRepository#save")
        db.save(event.toWrapper())
    }

    override suspend fun findByIdOrNull(scan: DocumentScanId): DocumentScan? {
        val events = db.findByPartitionKey(scan.value)
            .map { it.toDomain() }
            .takeIf { it.isNotEmpty() } ?: return null
        return DocumentScan.build(events)
    }

    override suspend fun existsIdScanDigest(idScanDigest: String): Boolean {
        return db.findByPrimaryKeyOnIndex(
            index = GlobalSecondaryIndexes.GSIndex1,
            partitionKey = DOCUMENT_SCAN_DIGEST_PREFIX + idScanDigest,
        ).any()
    }
}

private fun DocumentScanEvent.getCreatedTimestamp() = created.toInstant().toEpochMilli()

private fun getExternalId(event: DocumentScanEvent) =
    if (event is DocumentScanCreated) {
        event.externalId
    } else {
        null
    }

private fun getIndex1HashKey(event: DocumentScanEvent) =
    when (event) {
        is DocumentProcessed -> DOCUMENT_SCAN_DIGEST_PREFIX + event.idScanDigest
        is DocumentScanCompleted -> DOCUMENT_SCAN_DIGEST_PREFIX + event.idScanDigest
        is DocumentScanError -> DOCUMENT_SCAN_DIGEST_PREFIX + event.idScanDigest
        is DocumentScanCreated -> EXTERNAL_ID_PREFIX + event.externalId.value
        is DocumentScanSessionCreated -> null
    }

private fun getScanResultBlob(event: DocumentScanEvent) =
    when (event) {
        is DocumentProcessed -> event.blob
        is DocumentScanCompleted -> event.blob
        is DocumentScanSessionCreated, is DocumentScanCreated -> null
        is DocumentScanError -> event.blob
    }

private fun getIndex1ScanKey(event: DocumentScanEvent) = "${event.eventType.name}#${event.getCreatedTimestamp()}"

private fun DocumentScanEvent.toEntity(): DocumentScanEventEntity {
    return when (this) {
        is DocumentScanCreated -> DocumentScanCreatedEntity(
            documentScanId = id.value,
            clientId = clientId.value,
            externalId = externalId.value,
            created = created,
        )

        is DocumentScanSessionCreated -> ScanSessionCreatedEntity(
            documentScanId = id.value,
            deviceKeyIdentifier = deviceKeyIdentifier,
            userAgent = userAgent,
            clientIpAddress = clientIpAddress,
            created = created,
        )

        is DocumentProcessed -> DocumentProcessedEntity(
            documentScanId = id.value,
            idScan = idScan,
            idScanDigest = idScanDigest,
            frontImage = frontImage,
            backImage = backImage,
            scannedIDPhotoFaceFoundWithMinimumQuality = scannedIDPhotoFaceFoundWithMinimumQuality,
            didCompleteIDScanWithoutMatchingOCRTemplate = didCompleteIDScanWithoutMatchingOCRTemplate,
            faceOnDocument = faceOnDocument,
            textOnDocument = textOnDocument,
            mrzStatus = mrzStatus,
            digitalSpoofStatus = digitalSpoofStatus,
            documentData = documentData,
            sessionExtraFlags = sessionExtraFlags,
            securityCheck = securityCheck,
            isDone = isDone,
            nextStep = nextStep,
            blob = blob,
            created = created,
            documentsRegion = documentsRegion,
            documentsBucket = documentsBucket,
        )

        is DocumentScanCompleted -> DocumentScanCompletedEntity(
            documentScanId = id.value,
            idScan = idScan,
            idScanDigest = idScanDigest,
            frontImage = frontImage,
            backImage = backImage,
            scannedIDPhotoFaceFoundWithMinimumQuality = scannedIDPhotoFaceFoundWithMinimumQuality,
            didCompleteIDScanWithoutMatchingOCRTemplate = didCompleteIDScanWithoutMatchingOCRTemplate,
            faceOnDocument = faceOnDocument,
            textOnDocument = textOnDocument,
            mrzStatus = mrzStatus,
            digitalSpoofStatus = digitalSpoofStatus,
            documentData = documentData,
            sessionExtraFlags = sessionExtraFlags,
            securityCheck = securityCheck,
            isDone = isDone,
            nextStep = nextStep,
            blob = blob,
            created = created,
            documentsRegion = documentsRegion,
            documentsBucket = documentsBucket,
        )

        is DocumentScanError -> DocumentScanErrorEntity(
            documentScanId = id.value,
            created = created,
            idScan = idScan,
            idScanDigest = idScanDigest,
            frontImage = frontImage,
            backImage = backImage,
            securityCheck = securityCheck,
            nextStep = nextStep,
            blob = blob,
            documentsRegion = documentsRegion,
            documentsBucket = documentsBucket,
        )
    }
}

private fun DocumentScanEvent.toWrapper(): DocumentScanEventWrapperEntity {
    val id = this.id
    val timestamp = this.getCreatedTimestamp()
    val externalId = getExternalId(this)
    val index1HashKey = getIndex1HashKey(this)
    val index1ScanKey = getIndex1ScanKey(this)
    val eventEntity = this.toEntity()
    val scanResultBlob = getScanResultBlob(this)
    val documentScanId = DocumentScanId(id.value)
    return DocumentScanEventWrapperEntity().apply {
        primaryKey = id.value
        scanKey = timestamp
        this.documentScanId = documentScanId
        this.externalId = externalId
        details = getObjectMapper().writeValueAsString(eventEntity)
        createdAt = eventEntity.created
        this.index1HashKey = index1HashKey
        this.index1ScanKey = index1ScanKey
        this.scanResultBlob = scanResultBlob
    }
}

private fun DocumentScanEventWrapperEntity.toDomain(): DocumentScanEvent {
    return when (val eventDetails = parseObjectFrom<DocumentScanEventEntity>(this.details)) {
        is DocumentScanCreatedEntity -> DocumentScanCreated(
            id = DocumentScanId(eventDetails.documentScanId),
            created = eventDetails.created,
            clientId = LivenessClientId(eventDetails.clientId),
            externalId = ExternalId(eventDetails.externalId),
        )

        is ScanSessionCreatedEntity -> DocumentScanSessionCreated(
            id = DocumentScanId(eventDetails.documentScanId),
            created = eventDetails.created,
            deviceKeyIdentifier = eventDetails.deviceKeyIdentifier,
            userAgent = eventDetails.userAgent,
            clientIpAddress = eventDetails.clientIpAddress,
        )

        is DocumentProcessedEntity -> DocumentProcessed(
            id = DocumentScanId(eventDetails.documentScanId),
            created = eventDetails.created,
            idScan = eventDetails.idScan,
            idScanDigest = eventDetails.idScanDigest,
            frontImage = eventDetails.frontImage,
            backImage = eventDetails.backImage,
            scannedIDPhotoFaceFoundWithMinimumQuality = eventDetails.scannedIDPhotoFaceFoundWithMinimumQuality,
            didCompleteIDScanWithoutMatchingOCRTemplate = eventDetails.didCompleteIDScanWithoutMatchingOCRTemplate,
            faceOnDocument = eventDetails.faceOnDocument,
            textOnDocument = eventDetails.textOnDocument,
            mrzStatus = eventDetails.mrzStatus,
            digitalSpoofStatus = eventDetails.digitalSpoofStatus,
            documentData = eventDetails.documentData,
            sessionExtraFlags = eventDetails.sessionExtraFlags,
            securityCheck = eventDetails.securityCheck,
            isDone = eventDetails.isDone,
            nextStep = eventDetails.nextStep,
            blob = eventDetails.blob,
            documentsRegion = eventDetails.documentsRegion,
            documentsBucket = eventDetails.documentsBucket,
        )

        is DocumentScanCompletedEntity -> DocumentScanCompleted(
            id = DocumentScanId(eventDetails.documentScanId),
            created = eventDetails.created,
            idScan = eventDetails.idScan,
            idScanDigest = eventDetails.idScanDigest,
            frontImage = eventDetails.frontImage,
            backImage = eventDetails.backImage,
            scannedIDPhotoFaceFoundWithMinimumQuality = eventDetails.scannedIDPhotoFaceFoundWithMinimumQuality,
            didCompleteIDScanWithoutMatchingOCRTemplate = eventDetails.didCompleteIDScanWithoutMatchingOCRTemplate,
            faceOnDocument = eventDetails.faceOnDocument,
            textOnDocument = eventDetails.textOnDocument,
            mrzStatus = eventDetails.mrzStatus,
            digitalSpoofStatus = eventDetails.digitalSpoofStatus,
            documentData = eventDetails.documentData,
            sessionExtraFlags = eventDetails.sessionExtraFlags,
            securityCheck = eventDetails.securityCheck,
            isDone = eventDetails.isDone,
            nextStep = eventDetails.nextStep,
            blob = eventDetails.blob,
            documentsRegion = eventDetails.documentsRegion,
            documentsBucket = eventDetails.documentsBucket,
        )

        is DocumentScanErrorEntity -> DocumentScanError(
            id = DocumentScanId(eventDetails.documentScanId),
            created = eventDetails.created,
            idScan = eventDetails.idScan,
            idScanDigest = eventDetails.idScanDigest,
            frontImage = eventDetails.frontImage,
            backImage = eventDetails.backImage,
            securityCheck = eventDetails.securityCheck,
            nextStep = eventDetails.nextStep,
            blob = eventDetails.blob,
            documentsRegion = eventDetails.documentsRegion,
            documentsBucket = eventDetails.documentsBucket,
        )
    }
}