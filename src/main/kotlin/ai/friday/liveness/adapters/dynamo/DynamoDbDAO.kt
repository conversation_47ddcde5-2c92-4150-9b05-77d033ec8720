package ai.friday.liveness.adapters.dynamo

import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedClient
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbTable
import software.amazon.awssdk.enhanced.dynamodb.TableSchema
import software.amazon.awssdk.enhanced.dynamodb.model.PutItemEnhancedRequest
import software.amazon.awssdk.enhanced.dynamodb.model.PutItemEnhancedResponse
import software.amazon.awssdk.services.dynamodb.model.ReturnValue

interface DynamoDAO<T> {
    fun args(): Pair<String, Class<T>>
}

abstract class AbstractDynamoDAO<T>(cli: DynamoDbEnhancedClient) : DynamoDAO<T> {
    private var table: DynamoDbTable<T>

    init {
        val (tableName, type) = this.args()

        table = cli.table(tableName, TableSchema.fromBean(type))
    }

    fun save(item: T) = table.putItem(item)

    fun saveWithResponse(item: T): PutItemEnhancedResponse<T> =
        table.putItemWithResponse(
            PutItemEnhancedRequest.builder<T>(item!!::class.java).item(item).returnValues(ReturnValue.ALL_OLD).build(),
        )

    fun findByPrimaryKey(key: AbstractKey<T>): T? = table.getItem(key)

    fun findByPartitionKey(partitionKey: String): List<T> = table.query(partitionKey)

    fun findByPrimaryKeyOnIndex(index: GlobalSecondaryIndexes, partitionKey: String, sortKey: String): List<T> =
        table.getItemsByIndex(index, partitionKey, sortKey)

    fun findByPrimaryKeyOnIndex(index: GlobalSecondaryIndexes, partitionKey: String): List<T> =
        table.getItemsByIndex(index, partitionKey)

    fun findLessThanOnIndex(index: GlobalSecondaryIndexes, partitionKey: String, sortKey: String): List<T> =
        table.getItemsByIndexSortLessThan(index, partitionKey, sortKey)

    fun findBeginsWithOnIndex(index: GlobalSecondaryIndexes, partitionKey: String, sortKey: String): List<T> =
        table.getItemsByIndexSortBeginsWith(index, partitionKey, sortKey)
}

abstract class AbstractKey<T>(
    val partitionKey: String,
    val sortKey: String,
)