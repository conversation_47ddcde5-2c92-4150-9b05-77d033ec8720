package ai.friday.liveness.adapters.dynamo

import ai.friday.liveness.ByteWrapper
import ai.friday.liveness.adapters.parser.getObjectMapper
import ai.friday.liveness.adapters.parser.parseObjectFrom
import ai.friday.liveness.app.LivenessClientId
import ai.friday.liveness.app.liveness.AgeEstimateGroup
import ai.friday.liveness.app.liveness.DuplicationVerified
import ai.friday.liveness.app.liveness.EnrollmentCreated
import ai.friday.liveness.app.liveness.ExternalId
import ai.friday.liveness.app.liveness.FaceMapUpdated
import ai.friday.liveness.app.liveness.FaceScanReceived
import ai.friday.liveness.app.liveness.FaceVerified
import ai.friday.liveness.app.liveness.FraudVerified
import ai.friday.liveness.app.liveness.Liveness
import ai.friday.liveness.app.liveness.LivenessEvent
import ai.friday.liveness.app.liveness.LivenessEventRepository
import ai.friday.liveness.app.liveness.LivenessEventType
import ai.friday.liveness.app.liveness.LivenessId
import ai.friday.liveness.app.liveness.LivenessProvider
import ai.friday.liveness.app.liveness.LivenessRawImages
import ai.friday.liveness.app.liveness.LivenessSecurityCheck
import ai.friday.liveness.app.liveness.MarkedAsFraud
import ai.friday.liveness.app.liveness.MatchCreated
import ai.friday.liveness.app.liveness.PlatformType
import ai.friday.liveness.app.liveness.SessionCreated
import com.fasterxml.jackson.annotation.JsonTypeInfo
import com.fasterxml.jackson.annotation.JsonTypeName
import jakarta.inject.Singleton
import java.time.ZonedDateTime
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedClient
import software.amazon.awssdk.enhanced.dynamodb.internal.converter.attribute.ZonedDateTimeAsStringAttributeConverter
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbAttribute
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbBean
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbConvertedBy
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbPartitionKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSecondaryPartitionKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSecondarySortKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSortKey

private const val EXTERNAL_ID_PREFIX = "EXTERNAL_ID#"
private const val FACE_SCAN_DIGEST_PREFIX = "FACE_SCAN_DIGEST#"
private const val FACE_MAP_PREFIX = "FACE_MAP#"

@Singleton
class LivenessEventDynamoDbRepository(cli: DynamoDbEnhancedClient) : LivenessEventRepository {

    private val db = LivenessEventWrapperDynamoDAO(cli)

    override suspend fun save(event: LivenessEvent) = db.save(event.toWrapper())

    override suspend fun findByIdOrNull(livenessId: LivenessId): Liveness? {
        val events = db.findByPartitionKey(livenessId.value).map { it.toDomain() }
        if (events.isEmpty()) {
            return null
        }
        return Liveness.build(events)
    }

    override suspend fun findEnrollmentByExternalId(externalId: ExternalId): Liveness? {
        return db.findBeginsWithOnIndex(
            index = GlobalSecondaryIndexes.GSIndex1,
            partitionKey = EXTERNAL_ID_PREFIX + externalId.value,
            sortKey = "${LivenessEventType.ENROLLMENT_CREATED.name}#",
        ).lastOrNull()?.let {
            findByIdOrNull(it.livenessId)
        }
    }

    override suspend fun findMatchByExternalId(externalId: ExternalId): List<Liveness> {
        return db.findBeginsWithOnIndex(
            index = GlobalSecondaryIndexes.GSIndex1,
            partitionKey = EXTERNAL_ID_PREFIX + externalId.value,
            sortKey = "${LivenessEventType.MATCH_CREATED.name}#",
        ).mapNotNull {
            findByIdOrNull(it.livenessId)
        }
    }

    override suspend fun existsFaceScanDigest(digest: String): Boolean {
        return db.findByPrimaryKeyOnIndex(
            index = GlobalSecondaryIndexes.GSIndex1,
            partitionKey = FACE_SCAN_DIGEST_PREFIX + digest,
        ).any()
    }

    override suspend fun findRawImagesData(livenessId: LivenessId): LivenessRawImages? {
        return db.findByPrimaryKeyOnIndex(
            index = GlobalSecondaryIndexes.GSIndex1,
            partitionKey = FACE_MAP_PREFIX + livenessId.value,
        ).firstOrNull { it.faceMap != null }?.let {
            LivenessRawImages(
                faceMap = ByteWrapper(it.faceMap!!).bytes,
                auditTrailImage = it.auditTrailImage?.let { auditTrailImage -> ByteWrapper(auditTrailImage).bytes },
            )
        }
    }

    // Método para testes - buscar todos os eventos de um liveness
    suspend fun findAllEventsByLivenessId(livenessId: LivenessId): List<LivenessEvent> {
        return db.findByPartitionKey(livenessId.value).map { it.toDomain() }
    }
}

private class LivenessEventWrapperDynamoDAO(cli: DynamoDbEnhancedClient) :
    AbstractDynamoDAO<LivenessEventWrapperEntity>(cli) {
    override fun args() = LIVENESS_TABLE_NAME to LivenessEventWrapperEntity::class.java
}

private fun LivenessEvent.toEntity(): LivenessEventEntity {
    return when (this) {
        is EnrollmentCreated -> EnrollmentCreatedEntity(
            livenessId = this.livenessId.value,
            created = this.created,
            clientId = this.clientId.value,
            externalId = this.externalId.value,
            provider = this.provider.value,
        )

        is MatchCreated -> MatchCreatedEntity(
            livenessId = this.livenessId.value,
            created = this.created,
            clientId = this.clientId.value,
            externalId = this.externalId.value,
            provider = this.provider.value,
        )

        is SessionCreated -> SessionCreatedEntity(
            livenessId = this.livenessId.value,
            created = this.created,
            deviceKeyIdentifier = this.deviceKeyIdentifier,
            userAgent = this.userAgent,
            clientIpAddress = this.clientIpAddress,
        )

        is FaceVerified -> FaceVerifiedEntity(
            livenessId = this.livenessId.value,
            created = this.created,
            success = this.success,
            ageEstimateGroup = this.ageEstimateGroup,
            platformType = this.platformType,
            deviceSDKVersion = this.deviceSDKVersion,
            sessionTokenCheck = this.securityCheck.sessionTokenCheck,
            auditTrailVerificationCheck = this.securityCheck.auditTrailVerificationCheck,
            faceScanLivenessCheck = this.securityCheck.faceScanLivenessCheck,
        )

        is FaceScanReceived -> FaceScanReceivedEntity(
            livenessId = this.livenessId.value,
            created = this.created,
            faceScanDigest = this.faceScanDigest,
        )

        is DuplicationVerified -> DuplicationVerifiedEntity(
            livenessId = this.livenessId.value,
            created = this.created,
            duplications = this.duplications?.map { it.value },
        )

        is FaceMapUpdated -> FaceMapUpdatedEntity(
            livenessId = this.livenessId.value,
            created = this.created,
        )

        is MarkedAsFraud -> MarkedAsFraudEntity(
            livenessId = this.livenessId.value,
            created = this.created,
            externalId = this.externalId.value,

        )

        is FraudVerified -> FraudVerifiedEntity(
            livenessId = this.livenessId.value,
            created = this.created,
            fraudIndications = this.userList?.map { it.value },

        )
    }
}

private fun LivenessEventWrapperEntity.toDomain(): LivenessEvent {
    return when (val eventDetails = parseObjectFrom<LivenessEventEntity>(this.details)) {
        is EnrollmentCreatedEntity -> EnrollmentCreated(
            livenessId = LivenessId(eventDetails.livenessId),
            created = eventDetails.created,
            clientId = LivenessClientId(eventDetails.clientId),
            externalId = ExternalId(eventDetails.externalId),
            provider = LivenessProvider.values().find { it.value == eventDetails.provider } ?: LivenessProvider.FACETECH,
        )

        is MatchCreatedEntity -> MatchCreated(
            livenessId = LivenessId(eventDetails.livenessId),
            created = eventDetails.created,
            clientId = LivenessClientId(eventDetails.clientId),
            externalId = ExternalId(eventDetails.externalId),
            provider = LivenessProvider.values().find { it.value == eventDetails.provider } ?: LivenessProvider.FACETECH,
        )

        is SessionCreatedEntity -> SessionCreated(
            livenessId = LivenessId(eventDetails.livenessId),
            created = eventDetails.created,
            deviceKeyIdentifier = eventDetails.deviceKeyIdentifier,
            userAgent = eventDetails.userAgent,
            clientIpAddress = eventDetails.clientIpAddress,
        )

        is FaceVerifiedEntity -> FaceVerified(
            livenessId = LivenessId(eventDetails.livenessId),
            created = eventDetails.created,
            success = eventDetails.success,
            ageEstimateGroup = eventDetails.ageEstimateGroup,
            platformType = eventDetails.platformType,
            deviceSDKVersion = eventDetails.deviceSDKVersion,
            securityCheck = LivenessSecurityCheck(
                sessionTokenCheck = eventDetails.sessionTokenCheck,
                auditTrailVerificationCheck = eventDetails.auditTrailVerificationCheck,
                faceScanLivenessCheck = eventDetails.faceScanLivenessCheck,
            ),
            faceMap = this.faceMap,
            auditTrailImage = this.auditTrailImage,
            scanResultBlob = this.scanResultBlob!!,
        )

        is FaceScanReceivedEntity -> FaceScanReceived(
            livenessId = LivenessId(eventDetails.livenessId),
            created = eventDetails.created,
            faceScanDigest = eventDetails.faceScanDigest,
        )

        is DuplicationVerifiedEntity -> DuplicationVerified(
            livenessId = LivenessId(eventDetails.livenessId),
            created = eventDetails.created,
            duplications = eventDetails.duplications?.map { ExternalId(it) }?.toSet(),
        )

        is FaceMapUpdatedEntity -> FaceMapUpdated(
            livenessId = LivenessId(eventDetails.livenessId),
            created = eventDetails.created,
            updatedFaceMap = this.faceMap!!,
        )

        is MarkedAsFraudEntity -> MarkedAsFraud(
            livenessId = LivenessId(eventDetails.livenessId),
            created = eventDetails.created,
            externalId = ExternalId(eventDetails.externalId),
        )

        is FraudVerifiedEntity -> FraudVerified(
            livenessId = LivenessId(eventDetails.livenessId),
            created = eventDetails.created,
            userList = eventDetails.fraudIndications?.map { ExternalId(it) }?.toSet(),
        )
    }
}

private fun LivenessEvent.toWrapper(): LivenessEventWrapperEntity {
    val livenessId = this.livenessId
    val timestamp = this.getCreatedTimestamp()
    val externalId = getExternalId(this)
    val index1HashKey = getIndex1HashKey(this)
    val index1ScanKey = getIndex1ScanKey(this)
    val eventEntity = this.toEntity()
    val faceMap = getFaceMap(this)
    val auditTrailImage = getAuditTrailImage(this)
    val scanResultBlob = getScanResultBlob(this)
    return LivenessEventWrapperEntity().apply {
        primaryKey = livenessId.value
        scanKey = timestamp
        this.livenessId = livenessId
        this.externalId = externalId
        details = getObjectMapper().writeValueAsString(eventEntity)
        createdAt = eventEntity.created
        this.index1HashKey = index1HashKey
        this.index1ScanKey = index1ScanKey
        this.faceMap = faceMap
        this.auditTrailImage = auditTrailImage
        this.scanResultBlob = scanResultBlob
        this.duplications = this.duplications
        this.fraudIndications = this.fraudIndications
    }
}

private fun getScanResultBlob(livenessEvent: LivenessEvent): String? {
    if (livenessEvent is FaceVerified) {
        return livenessEvent.scanResultBlob
    }
    return null
}

private fun getFaceMap(livenessEvent: LivenessEvent): String? {
    if (livenessEvent is FaceVerified) {
        return livenessEvent.faceMap
    }
    if (livenessEvent is FaceMapUpdated) {
        return livenessEvent.updatedFaceMap
    }
    return null
}

private fun getFraudVerified(livenessEvent: LivenessEvent): Boolean = livenessEvent is FraudVerified
private fun getDuplicationVerified(livenessEvent: LivenessEvent): Boolean = livenessEvent is DuplicationVerified
private fun getMarkedAsFraud(livenessEvent: LivenessEvent): Boolean = livenessEvent is MarkedAsFraud

private fun getAuditTrailImage(livenessEvent: LivenessEvent): String? {
    if (livenessEvent is FaceVerified) {
        return livenessEvent.auditTrailImage
    }
    return null
}

private fun LivenessEvent.getCreatedTimestamp(): Long {
    return created.toInstant().toEpochMilli()
}

private fun getIndex1HashKey(event: LivenessEvent): String? {
    return when (event) {
        is EnrollmentCreated -> {
            EXTERNAL_ID_PREFIX + event.externalId.value
        }

        is MatchCreated -> {
            EXTERNAL_ID_PREFIX + event.externalId.value
        }

        is MarkedAsFraud -> EXTERNAL_ID_PREFIX + event.externalId.value

        is FaceScanReceived -> {
            FACE_SCAN_DIGEST_PREFIX + event.faceScanDigest
        }

        is FaceVerified, is FaceMapUpdated -> {
            FACE_MAP_PREFIX + event.livenessId.value
        }

        is SessionCreated -> null

        is DuplicationVerified -> null

        is FraudVerified -> null
    }
}

private fun getIndex1ScanKey(event: LivenessEvent): String {
    return "${event.eventType.name}#${event.getCreatedTimestamp()}"
}

private fun getExternalId(event: LivenessEvent): ExternalId? {
    return if (event is EnrollmentCreated) {
        event.externalId
    } else {
        null
    }
}

@JsonTypeInfo(use = JsonTypeInfo.Id.NAME, include = JsonTypeInfo.As.PROPERTY)
@JsonTypeName("LivenessEvent")
sealed class LivenessEventEntity {
    abstract val livenessId: String
    abstract val created: ZonedDateTime
    abstract val eventType: LivenessEventType
}

@JsonTypeName("EnrollmentCreated")
data class EnrollmentCreatedEntity(
    override val livenessId: String,
    override val created: ZonedDateTime,
    val clientId: String,
    val externalId: String,
    val provider: String = LivenessProvider.FACETECH.value,
) : LivenessEventEntity() {
    override val eventType = LivenessEventType.ENROLLMENT_CREATED
}

@JsonTypeName("MatchCreated")
data class MatchCreatedEntity(
    override val livenessId: String,
    override val created: ZonedDateTime,
    val clientId: String,
    val externalId: String,
    val provider: String = LivenessProvider.FACETECH.value,
) : LivenessEventEntity() {
    override val eventType = LivenessEventType.MATCH_CREATED
}

@JsonTypeName("MarkedAsFraud")
data class MarkedAsFraudEntity(
    override val livenessId: String,
    override val created: ZonedDateTime,
    val externalId: String,
) : LivenessEventEntity() {
    override val eventType = LivenessEventType.MARKED_AS_FRAUD
}

@JsonTypeName("FraudVerified")
data class FraudVerifiedEntity(
    override val livenessId: String,
    override val created: ZonedDateTime,
    val fraudIndications: List<String>?,
) : LivenessEventEntity() {
    override val eventType = LivenessEventType.FRAUD_VERIFIED
}

@JsonTypeName("SessionCreated")
data class SessionCreatedEntity(
    override val livenessId: String,
    override val created: ZonedDateTime,
    val deviceKeyIdentifier: String,
    val userAgent: String,
    val clientIpAddress: String,
) : LivenessEventEntity() {
    override val eventType = LivenessEventType.SESSION_CREATED
}

@JsonTypeName("FaceVerified")
data class FaceVerifiedEntity(
    override val livenessId: String,
    override val created: ZonedDateTime,
    val success: Boolean,
    val ageEstimateGroup: AgeEstimateGroup,
    val platformType: PlatformType,
    val deviceSDKVersion: String,
    val sessionTokenCheck: Boolean,
    val auditTrailVerificationCheck: Boolean,
    val faceScanLivenessCheck: Boolean,
) : LivenessEventEntity() {
    override val eventType = LivenessEventType.FACE_VERIFIED
}

@JsonTypeName("DuplicationVerified")
data class DuplicationVerifiedEntity(
    override val livenessId: String,
    override val created: ZonedDateTime,
    val duplications: List<String>?,

) : LivenessEventEntity() {
    override val eventType = LivenessEventType.DUPLICATION_VERIFIED
}

@JsonTypeName("FaceMapUpdated")
data class FaceMapUpdatedEntity(
    override val livenessId: String,
    override val created: ZonedDateTime,
) : LivenessEventEntity() {
    override val eventType = LivenessEventType.FACE_MAP_UPDATED
}

@JsonTypeName("FaceScanReceived")
data class FaceScanReceivedEntity(
    override val livenessId: String,
    override val created: ZonedDateTime,
    val faceScanDigest: String,
) : LivenessEventEntity() {
    override val eventType = LivenessEventType.FACE_SCAN_RECEIVED
}

@DynamoDbBean
class LivenessEventWrapperEntity {
    @get:DynamoDbPartitionKey
    @get:DynamoDbAttribute(value = PARTITION_KEY)
    lateinit var primaryKey: String // LIVENESS_ID

    @get:DynamoDbSortKey
    @get:DynamoDbAttribute(value = SCAN_KEY)
    var scanKey: Long = 0 // TIMESTAMP

    @get:DynamoDbConvertedBy(value = LivenessIdConverter::class)
    @get:DynamoDbAttribute(value = LIVENESS_ID)
    lateinit var livenessId: LivenessId

    @get:DynamoDbConvertedBy(value = ExternalIdConverter::class)
    @get:DynamoDbAttribute(value = EXTERNAL_ID)
    var externalId: ExternalId? = null

    @get:DynamoDbAttribute(value = "Details")
    lateinit var details: String

    @get:DynamoDbConvertedBy(value = ZonedDateTimeAsStringAttributeConverter::class)
    @get:DynamoDbAttribute(value = CREATED_AT)
    lateinit var createdAt: ZonedDateTime

    @get:DynamoDbSecondaryPartitionKey(indexNames = [INDEX_1])
    @get:DynamoDbAttribute(value = INDEX_1_PARTITION_KEY)
    var index1HashKey: String? = null // EXTERNAL_ID

    @get:DynamoDbSecondarySortKey(indexNames = [INDEX_1])
    @get:DynamoDbAttribute(value = INDEX_1_SCAN_KEY)
    var index1ScanKey: String? = null // LIVENESS_ID#TIMESTAMP

    @get:DynamoDbAttribute(value = "ScanResultBlob")
    var scanResultBlob: String? = null

    @get:DynamoDbAttribute(value = "FaceMap")
    var faceMap: String? = null

    @get:DynamoDbAttribute(value = "AuditTrailImage")
    var auditTrailImage: String? = null

    @get:DynamoDbConvertedBy(value = ExternalIdConverter::class)
    @get:DynamoDbAttribute(value = "Duplications")
    var duplications: List<ExternalId>? = null

    @get:DynamoDbConvertedBy(value = ExternalIdConverter::class)
    @get:DynamoDbAttribute(value = "FraudIndication")
    var fraudIndications: List<ExternalId>? = null
}