package ai.friday.liveness.adapters.dynamo

import software.amazon.awssdk.enhanced.dynamodb.DynamoDbIndex
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbTable
import software.amazon.awssdk.enhanced.dynamodb.Key
import software.amazon.awssdk.enhanced.dynamodb.model.QueryConditional
import software.amazon.awssdk.enhanced.dynamodb.model.QueryEnhancedRequest

fun <T> DynamoDbTable<T>.getItem(key: AbstractKey<T>): T? =
    this.item(key.partitionKey, key.sortKey)

fun <T> DynamoDbTable<T>.query(partitionKey: String): List<T> =
    this.query(queryByPartitionKey(partitionKey))
        .flatMap { it.items() }

fun <T> DynamoDbTable<T>.getItemsByIndex(
    index: GlobalSecondaryIndexes,
    partitionKey: String,
    sortKey: String,
): List<T> =
    this.index(index.name)
        .query(partitionKey, sortKey)
        .flatMap { it.items() }

fun <T> DynamoDbTable<T>.getItemsByIndex(index: GlobalSecondaryIndexes, partitionKey: String): List<T> =
    this.index(index.name)
        .query(partitionKey)
        .flatMap { it.items() }

fun <T> DynamoDbTable<T>.getItemsByIndexSortLessThan(
    index: GlobalSecondaryIndexes,
    partitionKey: String,
    sortKey: String,
): List<T> =
    this.index(index.name)
        .query(
            QueryEnhancedRequest.builder()
                .queryConditional(QueryConditional.sortLessThan(keyByPartitionKeyAndSortKey(partitionKey, sortKey)))
                .build(),
        )
        .flatMap { it.items() }

fun <T> DynamoDbTable<T>.getItemsByIndexSortBeginsWith(
    index: GlobalSecondaryIndexes,
    partitionKey: String,
    sortKey: String,
): List<T> =
    this.index(index.name)
        .query(
            QueryEnhancedRequest.builder()
                .queryConditional(QueryConditional.sortBeginsWith(keyByPartitionKeyAndSortKey(partitionKey, sortKey)))
                .build(),
        )
        .flatMap { it.items() }

private fun <T> DynamoDbTable<T>.item(partitionKey: String, sortKey: String) =
    this.getItem(keyByPartitionKeyAndSortKey(partitionKey, sortKey))

private fun <T> DynamoDbIndex<T>.query(partitionKey: String, sortKey: String) =
    this.query(queryByPartitionKeyAndSortKey(partitionKey, sortKey))

private fun <T> DynamoDbIndex<T>.query(partitionKey: String) =
    this.query(queryByPartitionKey(partitionKey))

private fun queryByPartitionKeyAndSortKey(partitionKey: String, sortKey: String) =
    QueryEnhancedRequest.builder()
        .queryConditional(conditionalByPartitionKeyAndSortKey(partitionKey, sortKey))
        .build()

private fun queryByPartitionKey(partitionKey: String) =
    QueryEnhancedRequest.builder()
        .queryConditional(conditionalByPartitionKey(partitionKey))
        .build()

private fun conditionalByPartitionKeyAndSortKey(partitionKey: String, sortKey: String) =
    QueryConditional.keyEqualTo(keyByPartitionKeyAndSortKey(partitionKey, sortKey))

private fun conditionalByPartitionKey(partitionKey: String) =
    QueryConditional.keyEqualTo(keyByPartitionKey(partitionKey))

private fun keyByPartitionKey(partitionKey: String) =
    Key.builder()
        .partitionValue(partitionKey)
        .build()

private fun keyByPartitionKeyAndSortKey(partitionKey: String, sortKey: String) =
    Key.builder()
        .partitionValue(partitionKey)
        .sortValue(sortKey)
        .build()