package ai.friday.liveness.adapters.dynamo.documentscan

import ai.friday.liveness.adapters.dynamo.CREATED_AT
import ai.friday.liveness.adapters.dynamo.DOCUMENT_SCAN_ID
import ai.friday.liveness.adapters.dynamo.EXTERNAL_ID
import ai.friday.liveness.adapters.dynamo.ExternalIdConverter
import ai.friday.liveness.adapters.dynamo.INDEX_1
import ai.friday.liveness.adapters.dynamo.INDEX_1_PARTITION_KEY
import ai.friday.liveness.adapters.dynamo.INDEX_1_SCAN_KEY
import ai.friday.liveness.adapters.dynamo.PARTITION_KEY
import ai.friday.liveness.adapters.dynamo.SCAN_KEY
import ai.friday.liveness.app.documentscan.DigitalSpoofStatus
import ai.friday.liveness.app.documentscan.DocumentScanEventType
import ai.friday.liveness.app.documentscan.DocumentScanId
import ai.friday.liveness.app.documentscan.DocumentScanSecurityCheck
import ai.friday.liveness.app.documentscan.FaceOnDocumentStatus
import ai.friday.liveness.app.documentscan.MrzStatus
import ai.friday.liveness.app.documentscan.NextStep
import ai.friday.liveness.app.documentscan.TextOnDocumentStatus
import ai.friday.liveness.app.liveness.ExternalId
import com.fasterxml.jackson.annotation.JsonTypeInfo
import com.fasterxml.jackson.annotation.JsonTypeName
import java.time.ZonedDateTime
import software.amazon.awssdk.enhanced.dynamodb.internal.converter.attribute.ZonedDateTimeAsStringAttributeConverter
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbAttribute
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbBean
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbConvertedBy
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbPartitionKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSecondaryPartitionKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSecondarySortKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSortKey

@DynamoDbBean
class DocumentScanEventWrapperEntity {
    @get:DynamoDbPartitionKey
    @get:DynamoDbAttribute(value = PARTITION_KEY)
    lateinit var primaryKey: String // DOCUMENT_SCAN_ID

    @get:DynamoDbSortKey
    @get:DynamoDbAttribute(value = SCAN_KEY)
    var scanKey: Long = 0 // TIMESTAMP

    @get:DynamoDbConvertedBy(value = DocumentScanIdConverter::class)
    @get:DynamoDbAttribute(value = DOCUMENT_SCAN_ID)
    lateinit var documentScanId: DocumentScanId

    @get:DynamoDbConvertedBy(value = ExternalIdConverter::class)
    @get:DynamoDbAttribute(value = EXTERNAL_ID)
    var externalId: ExternalId? = null

    @get:DynamoDbAttribute(value = "Details")
    lateinit var details: String

    @get:DynamoDbConvertedBy(value = ZonedDateTimeAsStringAttributeConverter::class)
    @get:DynamoDbAttribute(value = CREATED_AT)
    lateinit var createdAt: ZonedDateTime

    @get:DynamoDbSecondaryPartitionKey(indexNames = [INDEX_1])
    @get:DynamoDbAttribute(value = INDEX_1_PARTITION_KEY)
    var index1HashKey: String? = null // EXTERNAL_ID

    @get:DynamoDbSecondarySortKey(indexNames = [INDEX_1])
    @get:DynamoDbAttribute(value = INDEX_1_SCAN_KEY)
    var index1ScanKey: String? = null // DOCUMENT_SCAN_ID#TIMESTAMP

    @get:DynamoDbAttribute(value = "ScanResultBlob")
    var scanResultBlob: String? = null
}

@JsonTypeInfo(use = JsonTypeInfo.Id.NAME, include = JsonTypeInfo.As.PROPERTY)
@JsonTypeName("DocumentScanEventEntity")
sealed class DocumentScanEventEntity {
    abstract val documentScanId: String
    abstract val created: ZonedDateTime
    abstract val eventType: DocumentScanEventType
}

@JsonTypeName("DocumentScanErrorEntity")
data class DocumentScanErrorEntity(
    override val documentScanId: String,
    override val created: ZonedDateTime,
    val idScan: String,
    val idScanDigest: String,
    val frontImage: String?,
    val backImage: String?,
    val securityCheck: DocumentScanSecurityCheck,
    val nextStep: NextStep,
    val blob: String,
    val documentsRegion: String,
    val documentsBucket: String,
) : DocumentScanEventEntity() {
    override val eventType = DocumentScanEventType.ERROR
}

@JsonTypeName("DocumentScanCreatedEntity")
data class DocumentScanCreatedEntity(
    override val documentScanId: String,
    override val created: ZonedDateTime,
    val clientId: String,
    val externalId: String,
) : DocumentScanEventEntity() {
    override val eventType = DocumentScanEventType.CREATED
}

@JsonTypeName("ScanSessionCreatedEntity")
data class ScanSessionCreatedEntity(
    override val documentScanId: String,
    override val created: ZonedDateTime,
    val deviceKeyIdentifier: String,
    val userAgent: String,
    val clientIpAddress: String,
) : DocumentScanEventEntity() {
    override val eventType = DocumentScanEventType.SESSION_CREATED
}

@JsonTypeName("DocumentProcessedEntity")
data class DocumentProcessedEntity(
    override val documentScanId: String,
    override val created: ZonedDateTime,
    val idScan: String,
    val idScanDigest: String,
    val frontImage: String?,
    val backImage: String?,
    val scannedIDPhotoFaceFoundWithMinimumQuality: Boolean,
    val didCompleteIDScanWithoutMatchingOCRTemplate: Boolean,
    val faceOnDocument: FaceOnDocumentStatus,
    val textOnDocument: TextOnDocumentStatus,
    val mrzStatus: MrzStatus,
    val digitalSpoofStatus: DigitalSpoofStatus,
    val documentData: String,
    val sessionExtraFlags: String,
    val securityCheck: DocumentScanSecurityCheck,
    val isDone: Boolean,
    val nextStep: NextStep,
    val blob: String,
    val documentsRegion: String,
    val documentsBucket: String,
) : DocumentScanEventEntity() {
    override val eventType = DocumentScanEventType.PROCESSED
}

@JsonTypeName("DocumentScanCompletedEntity")
data class DocumentScanCompletedEntity(
    override val documentScanId: String,
    override val created: ZonedDateTime,
    val idScan: String,
    val idScanDigest: String,
    val frontImage: String,
    val backImage: String?,
    val scannedIDPhotoFaceFoundWithMinimumQuality: Boolean,
    val didCompleteIDScanWithoutMatchingOCRTemplate: Boolean,
    val faceOnDocument: FaceOnDocumentStatus,
    val textOnDocument: TextOnDocumentStatus,
    val mrzStatus: MrzStatus,
    val digitalSpoofStatus: DigitalSpoofStatus,
    val documentData: String,
    val sessionExtraFlags: String,
    val securityCheck: DocumentScanSecurityCheck,
    val isDone: Boolean,
    val nextStep: NextStep,
    val blob: String,
    val documentsRegion: String,
    val documentsBucket: String,
) : DocumentScanEventEntity() {
    override val eventType = DocumentScanEventType.COMPLETED
}