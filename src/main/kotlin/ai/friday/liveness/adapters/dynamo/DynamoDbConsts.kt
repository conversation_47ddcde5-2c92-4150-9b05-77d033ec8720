package ai.friday.liveness.adapters.dynamo

const val LIVENESS_TABLE_NAME = "LivenessGateway-LivenessEvent"
const val PARTITION_KEY = "PartitionKey" // String
const val SCAN_KEY = "ScanKey" // Long

const val INDEX_1 = "GSIndex1"
const val INDEX_1_PARTITION_KEY = "GSIndex1PartitionKey" // String
const val INDEX_1_SCAN_KEY = "GSIndex1ScanKey" // String

enum class GlobalSecondaryIndexes {
    GSIndex1,
}

const val LIVENESS_ID = "LivenessId" // String
const val LIVENESS_TYPE = "LivenessType" // String
const val LIVENESS_STATUS = "LivenessStatus" // String
const val EXTERNAL_ID = "ExternalId" // String
const val CREATED_AT = "CreatedAt" // String
const val UPDATED_AT = "UpdatedAt" // String

const val DOCUMENT_SCAN_ID = "DocumentScanId" // String