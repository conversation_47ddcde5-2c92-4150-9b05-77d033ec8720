package ai.friday.liveness.adapters.dynamo

import ai.friday.liveness.app.liveness.LivenessId
import software.amazon.awssdk.enhanced.dynamodb.AttributeConverter
import software.amazon.awssdk.enhanced.dynamodb.AttributeValueType
import software.amazon.awssdk.enhanced.dynamodb.EnhancedType
import software.amazon.awssdk.services.dynamodb.model.AttributeValue

class LivenessIdConverter : AttributeConverter<LivenessId> {
    override fun transformFrom(livenessId: LivenessId): AttributeValue =
        AttributeValue.builder().s(livenessId.value).build()

    override fun transformTo(attributeValue: AttributeValue?): LivenessId =
        attributeValue?.s()?.let { LivenessId(it) } ?: throw IllegalArgumentException("Failed to convert to LivenessId")

    override fun type(): EnhancedType<LivenessId> = EnhancedType.of(LivenessId::class.java)

    override fun attributeValueType(): AttributeValueType = AttributeValueType.S
}