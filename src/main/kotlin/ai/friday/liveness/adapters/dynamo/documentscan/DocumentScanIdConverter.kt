package ai.friday.liveness.adapters.dynamo.documentscan

import ai.friday.liveness.app.documentscan.DocumentScanId
import software.amazon.awssdk.enhanced.dynamodb.AttributeConverter
import software.amazon.awssdk.enhanced.dynamodb.AttributeValueType
import software.amazon.awssdk.enhanced.dynamodb.EnhancedType
import software.amazon.awssdk.services.dynamodb.model.AttributeValue

class DocumentScanIdConverter : AttributeConverter<DocumentScanId> {
    override fun transformFrom(id: DocumentScanId): AttributeValue =
        AttributeValue.builder().s(id.value).build()

    override fun transformTo(attributeValue: AttributeValue?): DocumentScanId =
        attributeValue?.s()?.let { DocumentScanId(it) } ?: throw IllegalArgumentException("Failed to convert to DocumentScanId")

    override fun type(): EnhancedType<DocumentScanId> = EnhancedType.of(DocumentScanId::class.java)

    override fun attributeValueType(): AttributeValueType = AttributeValueType.S
}