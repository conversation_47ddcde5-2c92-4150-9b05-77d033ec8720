package ai.friday.liveness.adapters.dynamo

import ai.friday.liveness.app.liveness.ExternalId
import software.amazon.awssdk.enhanced.dynamodb.AttributeConverter
import software.amazon.awssdk.enhanced.dynamodb.AttributeValueType
import software.amazon.awssdk.enhanced.dynamodb.EnhancedType
import software.amazon.awssdk.services.dynamodb.model.AttributeValue

class ExternalIdConverter : AttributeConverter<ExternalId> {
    override fun transformFrom(externalId: ExternalId): AttributeValue =
        AttributeValue.builder().s(externalId.value).build()

    override fun transformTo(attributeValue: AttributeValue?): ExternalId =
        attributeValue?.s()?.let { ExternalId(it) } ?: throw IllegalArgumentException("Failed to convert to ExtenalId")

    override fun type(): EnhancedType<ExternalId> = EnhancedType.of(ExternalId::class.java)

    override fun attributeValueType(): AttributeValueType = AttributeValueType.S
}