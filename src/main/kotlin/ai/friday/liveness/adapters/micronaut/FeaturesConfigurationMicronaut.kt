package ai.friday.liveness.adapters.micronaut

import ai.friday.liveness.app.liveness.FeatureConfiguration
import io.micronaut.context.annotation.ConfigurationInject
import io.micronaut.context.annotation.ConfigurationProperties

@ConfigurationProperties("features")
class FeaturesConfigurationMicronaut
@ConfigurationInject constructor(
    override val oneToManySearch: <PERSON><PERSON>an,
    override val allowedExternalIdPrefix: String,
) : FeatureConfiguration