package ai.friday.liveness.adapters.facetech

import ai.friday.liveness.ByteWrapper
import ai.friday.liveness.andAppend
import ai.friday.liveness.app.documentscan.DigitalSpoofStatus
import ai.friday.liveness.app.documentscan.DocumentScanAdapter
import ai.friday.liveness.app.documentscan.DocumentScanRequest
import ai.friday.liveness.app.documentscan.DocumentScanResult
import ai.friday.liveness.app.documentscan.DocumentScanSecurityCheck
import ai.friday.liveness.app.documentscan.FaceOnDocumentStatus
import ai.friday.liveness.app.documentscan.MrzStatus
import ai.friday.liveness.app.documentscan.NextStep
import ai.friday.liveness.app.documentscan.TextOnDocumentStatus
import ai.friday.liveness.app.documentscan.hasPassed
import ai.friday.liveness.app.errors.DocumentScanError
import ai.friday.liveness.app.errors.GeneralError
import ai.friday.liveness.app.errors.LivenessError
import ai.friday.liveness.app.errors.SessionError
import ai.friday.liveness.app.liveness.AgeEstimateGroup
import ai.friday.liveness.app.liveness.LivenessAdapter
import ai.friday.liveness.app.liveness.LivenessCheckRequest
import ai.friday.liveness.app.liveness.LivenessCheckResult
import ai.friday.liveness.app.liveness.LivenessSecurityCheck
import ai.friday.liveness.app.liveness.PlatformType
import ai.friday.liveness.app.session.SessionAdapter
import ai.friday.liveness.app.session.SessionTokenRequest
import ai.friday.liveness.app.session.SessionTokenResult
import arrow.core.Either
import arrow.core.left
import arrow.core.right
import com.facetec.servercore.FaceTecAgeEstimateGroup
import com.facetec.servercore.FaceTecAuditTrailVerificationStatus
import com.facetec.servercore.FaceTecBehaviorOverrides
import com.facetec.servercore.FaceTecDigitalSpoofStatus
import com.facetec.servercore.FaceTecFaceOnDocumentStatus
import com.facetec.servercore.FaceTecIDScanOnlyResult
import com.facetec.servercore.FaceTecLiveness3DResult
import com.facetec.servercore.FaceTecLiveness3DStatus
import com.facetec.servercore.FaceTecMRZStatus
import com.facetec.servercore.FaceTecMatch3D3DResult
import com.facetec.servercore.FaceTecMatchLevel
import com.facetec.servercore.FaceTecPhotoIDScanAuditTrailVerificationStatus
import com.facetec.servercore.FaceTecPhotoIDScanNextStep
import com.facetec.servercore.FaceTecPlatformType
import com.facetec.servercore.FaceTecSDKInstance
import com.facetec.servercore.FaceTecSessionTokenStatus
import com.facetec.servercore.FaceTecTextOnDocumentStatus
import io.micronaut.context.annotation.Primary
import io.micronaut.context.annotation.Requires
import jakarta.inject.Singleton
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

@Singleton
@Primary
@Requires(notEnv = ["test", "staging"])
open class FaceTecSDKAdapter(
    private val faceTecSDK: FaceTecSDKInstance,
) : LivenessAdapter, SessionAdapter, DocumentScanAdapter {

    override suspend fun createSession(request: SessionTokenRequest): Either<SessionError, SessionTokenResult> {
        val markers = Markers
            .append("operationId", request.operationId)
            .andAppend("type", request.type.name)
            .andAppend("clientIpAddress", request.clientIpAddress)
            .andAppend("userAgent", request.userAgent)
            .andAppend("deviceKeyIdentifier", request.deviceKeyIdentifier)

        logger.info(markers, "FaceTecSDKAdapter#session")

        return try {
            val sessionToken = faceTecSDK.generateSessionToken()
            SessionTokenResult(
                token = sessionToken,
            ).right()
        } catch (exception: Exception) {
            logger.error(markers, "FaceTecSDKAdapter#session", exception)

            GeneralError.ServerError(cause = exception).left()
        }
    }

    override suspend fun scan(request: DocumentScanRequest): Either<DocumentScanError, DocumentScanResult> {
        val frontImage = request.frontImage?.let { ByteWrapper(it).bytes }
        val backImage = request.backImage?.let { ByteWrapper(it).bytes }

        val result = faceTecSDK.idScan_only(ByteWrapper(request.idScan).bytes, frontImage, backImage)
        val securityCheck = securityCheck(result)
        val hasPassedSecurityChecks = securityCheck.hasPassed()
        val updatedBlob = result.getScanResultBlob(
            arrayOf(
                FaceTecBehaviorOverrides.SKIP_USER_CONFIRMATION,
                FaceTecBehaviorOverrides.SKIP_ARR,
            ),
        )
        return try {
            if (result.success && hasPassedSecurityChecks) {
                DocumentScanResult.Success(
                    scannedIDPhotoFaceFoundWithMinimumQuality = result.scannedIDPhotoFaceFoundWithMinimumQuality,
                    didCompleteIDScanWithoutMatchingOCRTemplate = result.didCompleteIDScanWithoutMatchingOCRTemplate,
                    faceOnDocument = result.faceOnDocumentStatus.toDomain(),
                    textOnDocument = result.textOnDocumentStatus.toDomain(),
                    mrzStatus = result.mrzStatus.toDomain(),
                    digitalSpoofStatus = result.digitalSpoofStatus.toDomain(),
                    securityCheck = securityCheck,
                    isDone = result.isCompletelyDone,
                    documentData = result.documentData,
                    sessionExtraFlags = result.sessionExtraFlags,
                    nextStep = result.nextStep.toDomain(),
                    blob = updatedBlob,
                ).right()
            } else {
                DocumentScanResult.Failed(
                    securityCheck = securityCheck,
                    nextStep = result.nextStep.toDomain(),
                    blob = updatedBlob,
                ).right()
            }.also { it.map { result -> logResult(result) } }
        } catch (exception: Exception) {
            logger.error(Markers.empty(), "FaceTecSDKAdapter#scan", exception)
            GeneralError.ServerError(cause = exception).left()
        }
    }

    override suspend fun checkLiveness(request: LivenessCheckRequest): Either<LivenessError, LivenessCheckResult> {
        val markers = Markers.append("request", request)

        logger.info(markers, "FaceTecSDKAdapter#check")

        return try {
            val faceScan = ByteWrapper(request.faceScan)
            val auditTrailImage = ByteWrapper(request.auditTrailImage)
            val livenessResult = faceTecSDK.checkLiveness3D(faceScan.bytes, auditTrailImage.bytes)
            val securityCheck = securityCheck(livenessResult)

            val ageEstimateGroup = estimateAge(livenessResult)
            val platformType = platformType(livenessResult)

            val scanResultBlobFactory = { retryRequired: Boolean -> livenessResult.getScanResultBlob(retryRequired) }

            return if (livenessResult.success) {
                LivenessCheckResult.createSuccessResult(
                    faceMap = ByteWrapper(livenessResult.faceMap),
                    ageEstimateGroup = ageEstimateGroup,
                    platformType = platformType,
                    deviceSDKVersion = livenessResult.deviceSDKVersion,
                    securityCheck = securityCheck,
                    scanResultBlobFactory = scanResultBlobFactory,
                )
            } else {
                LivenessCheckResult.createFailureResult(
                    ageEstimateGroup = ageEstimateGroup,
                    platformType = platformType,
                    deviceSDKVersion = livenessResult.deviceSDKVersion,
                    securityCheck = securityCheck,
                    scanResultBlobFactory = scanResultBlobFactory,
                )
            }.right()
        } catch (exception: Exception) {
            logger.error(markers, "FaceTecSDKAdapter#check", exception)

            GeneralError.ServerError(cause = exception).left()
        }
    }

    override suspend fun matchLiveness(
        request: LivenessCheckRequest,
        faceMap: ByteWrapper,
    ): Either<LivenessError, LivenessCheckResult> {
        val markers = Markers.append("request", request)

        return try {
            val faceScan = ByteWrapper(request.faceScan)
            val auditTrailImage = ByteWrapper(request.auditTrailImage)

            val match3D3DResult = faceTecSDK.match3D_3D(faceMap.bytes, faceScan.bytes, auditTrailImage.bytes)
            markers.andAppend("match3D3DRawResult", match3D3DResult)

            val shouldLearn =
                match3D3DResult?.continuousLearningFaceMap != null && match3D3DResult.matchLevel == FaceTecMatchLevel.LEVEL_15

            val securityCheck = securityCheck(match3D3DResult)
            val ageEstimateGroup = estimateAge(match3D3DResult)
            val platformType = platformType(match3D3DResult)

            val scanResultBlobFactory = { retryRequired: Boolean -> match3D3DResult.getScanResultBlob(retryRequired) }

            val result = if (match3D3DResult.success) {
                LivenessCheckResult.createSuccessResult(
                    faceMap = if (shouldLearn) {
                        ByteWrapper(match3D3DResult.continuousLearningFaceMap)
                    } else {
                        faceMap
                    },
                    ageEstimateGroup = ageEstimateGroup,
                    platformType = platformType,
                    deviceSDKVersion = match3D3DResult.deviceSDKVersion,
                    securityCheck = securityCheck,
                    scanResultBlobFactory = scanResultBlobFactory,
                )
            } else {
                LivenessCheckResult.createFailureResult(
                    ageEstimateGroup = ageEstimateGroup,
                    platformType = platformType,
                    deviceSDKVersion = match3D3DResult.deviceSDKVersion,
                    securityCheck = securityCheck,
                    scanResultBlobFactory = scanResultBlobFactory,
                )
            }

            logger.info(markers.andAppend("result", result), "FaceTecSDKAdapter#match")
            return result.right()
        } catch (exception: Exception) {
            logger.error(markers, "FaceTecSDKAdapter#match", exception)

            GeneralError.ServerError(cause = exception).left()
        }
    }

    override suspend fun extractAuditImageFromFacemap(faceMap: ByteArray): Either<LivenessError, ByteArray> {
        val auditData = faceTecSDK.getFaceMapAuditData(faceMap)

        logger.info(Markers.append("auditData", auditData), "FaceTecSDKAdapter#retrieveAuditImage")

        return auditData.auditImage.right()
    }

    private fun estimateAge(livenessResult: FaceTecMatch3D3DResult): AgeEstimateGroup {
        return estimateAge(livenessResult.livenessStatus, livenessResult.continuousLearningFaceMap)
    }

    private fun estimateAge(livenessResult: FaceTecLiveness3DResult): AgeEstimateGroup {
        return estimateAge(livenessResult.livenessStatus, livenessResult.faceMap)
    }

    private fun estimateAge(status: FaceTecLiveness3DStatus, faceMap: ByteArray?): AgeEstimateGroup {
        return if (status == FaceTecLiveness3DStatus.LIVENESS_PROVEN && faceMap != null) {
            when (faceTecSDK.estimateAge3D(faceMap).ageEstimateGroup) {
                FaceTecAgeEstimateGroup.UNDER13 -> AgeEstimateGroup.UNDER_THIRTEEN
                FaceTecAgeEstimateGroup.OVER13 -> AgeEstimateGroup.OVER_THIRTEEN
                FaceTecAgeEstimateGroup.OVER18 -> AgeEstimateGroup.OVER_EIGHTEEN
                FaceTecAgeEstimateGroup.OVER25 -> AgeEstimateGroup.OVER_TWENTY_FIVE
                FaceTecAgeEstimateGroup.OVER30 -> AgeEstimateGroup.OVER_THIRTY
                FaceTecAgeEstimateGroup.OVER22 -> AgeEstimateGroup.OVER_TWENTY_TWO
                else -> AgeEstimateGroup.UNKNOWN
            }
        } else {
            return AgeEstimateGroup.UNKNOWN
        }
    }

    private fun logResult(result: DocumentScanResult) {
        val log = when (result) {
            is DocumentScanResult.Failed -> "DocumentScanResult.Failed(securityCheck=${result.securityCheck}, nextStep=${result.nextStep})"
            is DocumentScanResult.Success -> "DocumentScanResult.Success(scannedIDPhotoFaceFoundWithMinimumQuality=${result.scannedIDPhotoFaceFoundWithMinimumQuality}, didCompleteIDScanWithoutMatchingOCRTemplate=${result.didCompleteIDScanWithoutMatchingOCRTemplate}, faceOnDocument=${result.faceOnDocument}, textOnDocument=${result.textOnDocument}, mrzStatus=${result.mrzStatus}, digitalSpoofStatus=${result.digitalSpoofStatus}, securityCheck=${result.securityCheck}, isDone=${result.isDone}, documentData=${result.documentData}, sessionExtraFlags=${result.sessionExtraFlags}, nextStep=${result.nextStep})"
        }

        logger.info(Markers.append("result", log), "FaceTecSDKAdapter#scan")
    }

    companion object {
        private val logger = LoggerFactory.getLogger(FaceTecSDKAdapter::class.java)
    }
}

private fun securityCheck(faceTecIDScanOnlyResult: FaceTecIDScanOnlyResult) =
    DocumentScanSecurityCheck(
        documentScanCheck = faceTecIDScanOnlyResult.frontAuditTrailImageVerificationStatus == FaceTecPhotoIDScanAuditTrailVerificationStatus.SUCCESS,
        auditTrailVerificationCheck = faceTecIDScanOnlyResult.backAuditTrailImageVerificationStatus == FaceTecPhotoIDScanAuditTrailVerificationStatus.SUCCESS,
        sessionTokenCheck = faceTecIDScanOnlyResult.sessionTokenStatus == FaceTecSessionTokenStatus.VALID || faceTecIDScanOnlyResult.sessionTokenStatus == FaceTecSessionTokenStatus.NOT_APPLICABLE,
    )

private fun securityCheck(livenessResult: FaceTecLiveness3DResult): LivenessSecurityCheck {
    return securityCheck(
        livenessResult.livenessStatus,
        livenessResult.auditTrailVerificationStatus,
        livenessResult.sessionTokenStatus,
    )
}

private fun securityCheck(livenessResult: FaceTecMatch3D3DResult): LivenessSecurityCheck {
    return securityCheck(
        livenessResult.livenessStatus,
        livenessResult.auditTrailVerificationStatus,
        livenessResult.sessionTokenStatus,
    )
}

private fun securityCheck(
    status: FaceTecLiveness3DStatus,
    auditTrailVerificationStatus: FaceTecAuditTrailVerificationStatus,
    sessionTokenStatus: FaceTecSessionTokenStatus,
): LivenessSecurityCheck {
    return LivenessSecurityCheck(
        faceScanLivenessCheck = status == FaceTecLiveness3DStatus.LIVENESS_PROVEN,
        auditTrailVerificationCheck = auditTrailVerificationStatus == FaceTecAuditTrailVerificationStatus.SUCCESS,
        sessionTokenCheck = sessionTokenStatus == FaceTecSessionTokenStatus.VALID || sessionTokenStatus == FaceTecSessionTokenStatus.NOT_APPLICABLE,
    )
}

private fun platformType(livenessResult: FaceTecMatch3D3DResult): PlatformType {
    return platformType(livenessResult.platformType)
}

private fun platformType(livenessResult: FaceTecLiveness3DResult): PlatformType {
    return platformType(livenessResult.platformType)
}

private fun platformType(type: FaceTecPlatformType?): PlatformType {
    return when (type) {
        FaceTecPlatformType.ANDROID -> PlatformType.ANDROID
        FaceTecPlatformType.IOS -> PlatformType.IOS
        FaceTecPlatformType.BROWSER -> PlatformType.BROWSER
        else -> PlatformType.UNKNOWN
    }
}

private fun FaceTecDigitalSpoofStatus.toDomain() = when (this) {
    FaceTecDigitalSpoofStatus.LIKELY_PHYSICAL_ID -> DigitalSpoofStatus.LIKELY_PHYSICAL_ID
    FaceTecDigitalSpoofStatus.COULD_NOT_CONFIDENTLY_DETERMINE_PHYSICAL_ID_USER_NEEDS_TO_RETRY -> DigitalSpoofStatus.COULD_NOT_CONFIDENTLY_DETERMINE_PHYSICAL_ID_USER_NEEDS_TO_RETRY
}

private fun FaceTecMRZStatus.toDomain() = when (this) {
    FaceTecMRZStatus.NO_MRZ_SPECIFIED_BY_TEMPLATE -> MrzStatus.NO_MRZ_SPECIFIED_BY_TEMPLATE
    FaceTecMRZStatus.MRZ_READ_BUT_CHECKSUM_FAILED -> MrzStatus.MRZ_READ_BUT_CHECKSUM_FAILED
    FaceTecMRZStatus.SUCCESS -> MrzStatus.SUCCESS
}

private fun FaceTecTextOnDocumentStatus.toDomain() = when (this) {
    FaceTecTextOnDocumentStatus.LIKELY_ORIGINAL_TEXT -> TextOnDocumentStatus.LIKELY_ORIGINAL_TEXT
    FaceTecTextOnDocumentStatus.NOT_AVAILABLE -> TextOnDocumentStatus.NOT_AVAILABLE
    FaceTecTextOnDocumentStatus.CANNOT_CONFIRM_ID_IS_AUTHENTIC -> TextOnDocumentStatus.CANNOT_CONFIRM_ID_IS_AUTHENTIC
}

private fun FaceTecFaceOnDocumentStatus.toDomain() = when (this) {
    FaceTecFaceOnDocumentStatus.LIKELY_ORIGINAL_FACE -> FaceOnDocumentStatus.LIKELY_ORIGINAL_FACE
    FaceTecFaceOnDocumentStatus.NOT_AVAILABLE -> FaceOnDocumentStatus.NOT_AVAILABLE
    FaceTecFaceOnDocumentStatus.CANNOT_CONFIRM_ID_IS_AUTHENTIC -> FaceOnDocumentStatus.CANNOT_CONFIRM_ID_IS_AUTHENTIC
    FaceTecFaceOnDocumentStatus.OCR_TEMPLATE_DOES_NOT_SUPPORT_DETECTION -> FaceOnDocumentStatus.OCR_TEMPLATE_DOES_NOT_SUPPORT_DETECTION
}

private fun FaceTecPhotoIDScanNextStep.toDomain() = when (this) {
    FaceTecPhotoIDScanNextStep.FRONT_RETRY -> NextStep.FRONT_RETRY
    FaceTecPhotoIDScanNextStep.BACK -> NextStep.BACK
    FaceTecPhotoIDScanNextStep.BACK_RETRY -> NextStep.BACK_RETRY
    FaceTecPhotoIDScanNextStep.USER_CONFIRM -> NextStep.USER_CONFIRM
    FaceTecPhotoIDScanNextStep.COMPLETE -> NextStep.COMPLETE
    FaceTecPhotoIDScanNextStep.NFC -> NextStep.NFC
}