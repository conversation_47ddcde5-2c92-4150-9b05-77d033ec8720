package ai.friday.liveness.adapters.facetech

import com.facetec.servercore.FaceTecInitializeConfig
import com.facetec.servercore.FaceTecSDK
import com.facetec.servercore.FaceTecSDKInstance
import io.micronaut.context.annotation.ConfigurationProperties
import io.micronaut.context.annotation.Factory
import jakarta.inject.Singleton
import org.slf4j.LoggerFactory

@ConfigurationProperties("integrations.facetec.sdk")
open class FaceTecSDKConfig {
    lateinit var deviceKeyIdentifier: String
    lateinit var serverKey: String
    lateinit var productionKeyText: String
    lateinit var faceMapEncryptionKey: String
    lateinit var usageLogsServerUri: String
}

@Factory // TODO - eager initialization?
open class FaceTecSDKInstanceFactory(private val sdkConfig: FaceTecSDKConfig) {
    @Singleton
    fun getFaceTecSDKInstance(): FaceTecSDKInstance {
        try {
            logger.info("Initializing FaceTecSDK")
            val faceTecInitializeConfig = FaceTecInitializeConfig()

            val fullPath = "http://" + sdkConfig.usageLogsServerUri + "/"

            return FaceTecSDK.initializeInProductionModeWithServer(
                fullPath,
                sdkConfig.deviceKeyIdentifier,
                sdkConfig.serverKey,
                sdkConfig.faceMapEncryptionKey,
                faceTecInitializeConfig,
            )
        } catch (e: Throwable) {
            e.printStackTrace()
            logger.error("FaceTec SDK initialization failed", e)
            throw e
        }
    }

    companion object {
        private val logger = LoggerFactory.getLogger(FaceTecSDKInstanceFactory::class.java)
    }
}