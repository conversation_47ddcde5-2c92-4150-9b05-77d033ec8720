package ai.friday.liveness.adapters.facetech

import ai.friday.liveness.ByteWrapper
import ai.friday.liveness.andAppend
import ai.friday.liveness.app.LivenessClientId
import ai.friday.liveness.app.errors.GeneralError
import ai.friday.liveness.app.errors.LivenessError
import ai.friday.liveness.app.errors.SessionError
import ai.friday.liveness.app.liveness.AgeEstimateGroup
import ai.friday.liveness.app.liveness.ExternalId
import ai.friday.liveness.app.liveness.FaceMapSearchAdapter
import ai.friday.liveness.app.liveness.FaceMatchGroup
import ai.friday.liveness.app.liveness.LivenessAdapter
import ai.friday.liveness.app.liveness.LivenessCheckRequest
import ai.friday.liveness.app.liveness.LivenessCheckResult
import ai.friday.liveness.app.liveness.LivenessSecurityCheck
import ai.friday.liveness.app.liveness.PlatformType
import ai.friday.liveness.app.session.SessionAdapter
import ai.friday.liveness.app.session.SessionTokenRequest
import ai.friday.liveness.app.session.SessionTokenResult
import arrow.core.Either
import arrow.core.left
import arrow.core.right
import io.micronaut.context.annotation.ConfigurationProperties
import io.micronaut.context.annotation.Requires
import io.micronaut.core.type.Argument
import io.micronaut.http.HttpRequest
import io.micronaut.http.MediaType
import io.micronaut.http.client.HttpClient
import io.micronaut.http.client.HttpClientConfiguration
import io.micronaut.http.client.annotation.Client
import io.micronaut.http.client.exceptions.HttpClientResponseException
import io.micronaut.runtime.ApplicationConfiguration
import jakarta.inject.Singleton
import java.time.Duration
import kotlinx.coroutines.reactive.awaitFirst
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

const val FAKE_SELFIE_IMAGE =
    "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"

@Singleton
@Requires(notEnv = ["production"])
open class StagingFaceTecSDKAdapter(
    @param:Client(
        configuration = FaceTecHttpConfiguration::class,
        value = "\${integrations.facetec.host}",
    ) private val httpClient: HttpClient,
    private val configuration: FaceTecConfiguration,
) : LivenessAdapter, FaceMapSearchAdapter, SessionAdapter {
    override suspend fun createSession(request: SessionTokenRequest): Either<SessionError, SessionTokenResult> {
        val markers = Markers
            .append("operationId", request.operationId)
            .andAppend("type", request.type.name)
            .andAppend("clientIpAddress", request.clientIpAddress)
            .andAppend("userAgent", request.userAgent)
            .andAppend("deviceKeyIdentifier", request.deviceKeyIdentifier)

        return try {
            logger.info(markers, "StagingFaceTecSDKAdapter#createSession")

            val httpRequest = HttpRequest.GET<SessionTokenResult>(configuration.createSessionUrlPath)
                .header("X-Device-Key", request.deviceKeyIdentifier)
                .header("X-User-Agent", request.userAgent)
                .header("X-Forwarded-For", request.clientIpAddress)
                .header("User-Agent", request.userAgent)
                .contentType(MediaType.APPLICATION_JSON_TYPE)
                .accept(MediaType.APPLICATION_JSON_TYPE)

            val httpResponse = httpClient.retrieve(
                httpRequest,
                Argument.of(FaceTecSessionTokenResultTO::class.java),
                Argument.of(String::class.java),
            ).awaitFirst()

            if (httpResponse.sessionToken.isEmpty()) {
                logger.error(
                    markers.andAppend("ErrorMessage", "Session Token is empty"),
                    "StagingFaceTecSDKAdapter#createSession",
                )
                return GeneralError.ServerError("Session token is empty").left()
            }

            logger.info(markers, "StagingFaceTecSDKAdapter#createSessionSuccess")

            SessionTokenResult(token = httpResponse.sessionToken).right()
        } catch (exception: HttpClientResponseException) {
            logger.error(
                markers.andAppend("status", exception.status).andAppend("body", exception.response.body()),
                "StagingFaceTecSDKAdapter#createSession",
                exception,
            )

            GeneralError.ServerError(cause = exception).left()
        } catch (exception: Exception) {
            logger.error(markers, "StagingFaceTecSDKAdapter#createSession", exception)

            GeneralError.ServerError(cause = exception).left()
        }
    }

    override suspend fun checkLiveness(request: LivenessCheckRequest): Either<LivenessError, LivenessCheckResult> {
        val markers = Markers.append("request", request)

        return try {
            logger.info(markers, "StagingFaceTecSDKAdapter#checkLiveness")

            val httpRequest = HttpRequest.POST(configuration.checkLivenessUrlPath, request.toCheckLivenessRequestTO())
                .header("X-Device-Key", request.deviceKeyIdentifier)
                .header("X-User-Agent", request.userAgent)
                .header("X-Forwarded-For", request.clientIpAddress)
                .header("User-Agent", request.userAgent)
                .contentType(MediaType.APPLICATION_JSON_TYPE)
                .accept(MediaType.APPLICATION_JSON_TYPE)

            val httpResponse = httpClient.retrieve(
                httpRequest,
                Argument.of(Map::class.java),
                Argument.of(String::class.java),
            ).awaitFirst()

            parseResponse(httpResponse).also {
                logger.info(markers.andAppend("livenessCheckResult", it), "StagingFaceTecSDKAdapter#checkLiveness")
            }.right()
        } catch (exception: HttpClientResponseException) {
            logger.error(
                markers.andAppend("status", exception.status)
                    .andAppend("body", exception.response.body()),
                "StagingFaceTecSDKAdapter#checkLiveness",
                exception,
            )

            GeneralError.ServerError(cause = exception).left()
        } catch (exception: Exception) {
            logger.error(markers, "StagingFaceTecSDKAdapter#checkLiveness", exception)

            GeneralError.ServerError(cause = exception).left()
        }
    }

    override suspend fun matchLiveness(
        request: LivenessCheckRequest,
        faceMap: ByteWrapper,
    ): Either<LivenessError, LivenessCheckResult> {
        return LivenessCheckResult.Success(
            retryRequired = false,
            ageEstimateGroup = AgeEstimateGroup.UNKNOWN,
            platformType = PlatformType.UNKNOWN,
            deviceSDKVersion = request.deviceKeyIdentifier,
            scanResultBlob = "scanResultBlob",
            securityCheck = LivenessSecurityCheck(
                faceScanLivenessCheck = true,
                auditTrailVerificationCheck = true,
                sessionTokenCheck = true,
            ),
            faceMap = faceMap,
        ).right()
    }

    override suspend fun extractAuditImageFromFacemap(faceMap: ByteArray): Either<LivenessError, ByteArray> {
        return faceMap.right()
    }

    companion object {
        private val logger = LoggerFactory.getLogger(StagingFaceTecSDKAdapter::class.java)
    }

    override suspend fun saveFaceMap(externalId: ExternalId, faceMap: ByteArray, group: FaceMatchGroup, clientId: LivenessClientId) {}

    override suspend fun search3D(faceMap: ByteArray, group: FaceMatchGroup, clientId: LivenessClientId): Either<Exception, List<ExternalId>> {
        return emptyList<ExternalId>().right()
    }
}

private data class FaceTecSessionTokenResultTO(
    val sessionToken: String,
)

data class CheckLivenessRequestTO(
    val faceScan: String,
    val auditTrailImage: String,
    val lowQualityAuditTrailImage: String?,
)

private fun LivenessCheckRequest.toCheckLivenessRequestTO() = CheckLivenessRequestTO(
    faceScan = faceScan,
    auditTrailImage = auditTrailImage,
    lowQualityAuditTrailImage = lowQualityAuditTrailImage,
)

private fun parseResponse(httpResponse: Map<*, *>): LivenessCheckResult {
    fun checkAge(value: Int?): AgeEstimateGroup {
        return when (value) {
            0 -> AgeEstimateGroup.UNDER_THIRTEEN
            1 -> AgeEstimateGroup.OVER_THIRTEEN
            2 -> AgeEstimateGroup.OVER_EIGHTEEN
            3 -> AgeEstimateGroup.OVER_TWENTY_FIVE
            4 -> AgeEstimateGroup.OVER_THIRTY
            5 -> AgeEstimateGroup.OVER_TWENTY_TWO
            else -> AgeEstimateGroup.UNKNOWN
        }
    }

    fun checkPlatformType(value: Int?): PlatformType {
        return when (value) {
            0 -> PlatformType.ANDROID
            1 -> PlatformType.IOS
            2 -> PlatformType.BROWSER
            else -> PlatformType.UNKNOWN
        }
    }

    val faceScanSecurityChecks = httpResponse["faceScanSecurityChecks"] as Map<*, *>
    val additionalSessionData = httpResponse["additionalSessionData"] as Map<*, *>

    val success = httpResponse["success"].toString().toBoolean()
    val ageEstimateGroup = checkAge(httpResponse["ageEstimateGroupEnumInt"].toString().toIntOrNull())
    val platformType = checkPlatformType(httpResponse["platformTypeEnumInt"].toString().toIntOrNull())
    val deviceSDKVersion = additionalSessionData["deviceSDKVersion"].toString()
    val scanResultBlob = httpResponse["scanResultBlob"].toString()
    val securityCheck = LivenessSecurityCheck(
        sessionTokenCheck = faceScanSecurityChecks["sessionTokenCheckSucceeded"].toString().toBoolean(),
        auditTrailVerificationCheck = faceScanSecurityChecks["auditTrailVerificationCheckSucceeded"].toString()
            .toBoolean(),
        faceScanLivenessCheck = faceScanSecurityChecks["faceScanLivenessCheckSucceeded"].toString()
            .toBoolean(),
    )

    return if (success) {
        LivenessCheckResult.createSuccessResult(
            ageEstimateGroup = ageEstimateGroup,
            platformType = platformType,
            deviceSDKVersion = deviceSDKVersion,
            securityCheck = securityCheck,
            faceMap = ByteWrapper(FAKE_SELFIE_IMAGE),
            scanResultBlobFactory = { scanResultBlob },
        )
    } else {
        LivenessCheckResult.createFailureResult(
            ageEstimateGroup = ageEstimateGroup,
            platformType = platformType,
            deviceSDKVersion = deviceSDKVersion,
            securityCheck = securityCheck,
            scanResultBlobFactory = { scanResultBlob },
        )
    }
}

@Singleton
class FaceTecHttpConfiguration(
    applicationConfiguration: ApplicationConfiguration,
    private val configuration: HttpClientConfiguration,
) : HttpClientConfiguration(applicationConfiguration) {
    init {
        setReadTimeout(Duration.ofMinutes(10))
        setConnectTimeout(Duration.ofMinutes(10))
    }

    override fun getConnectionPoolConfiguration(): ConnectionPoolConfiguration =
        configuration.connectionPoolConfiguration

    override fun getMaxContentLength(): Int = 20971520
}

@ConfigurationProperties("integrations.facetec")
class FaceTecConfiguration {
    lateinit var checkLivenessUrlPath: String
    lateinit var createSessionUrlPath: String
}