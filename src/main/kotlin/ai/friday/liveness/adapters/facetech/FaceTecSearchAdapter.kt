package ai.friday.liveness.adapters.facetech

import ai.friday.liveness.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.liveness.andAppend
import ai.friday.liveness.app.LivenessClientId
import ai.friday.liveness.app.auth.AuthConfig
import ai.friday.liveness.app.liveness.ExternalId
import ai.friday.liveness.app.liveness.FaceMapSearchAdapter
import ai.friday.liveness.app.liveness.FaceMatchGroup
import ai.friday.liveness.app.liveness.tenantName
import arrow.core.Either
import arrow.core.right
import com.facetec.servercore.FaceTec3D3DSearchGroup
import com.facetec.servercore.FaceTecMatchLevel
import com.facetec.servercore.FaceTecPersistedSearchException
import com.facetec.servercore.FaceTecSDKInstance
import com.facetec.servercore.SearchGroup
import io.micronaut.context.annotation.Property
import io.micronaut.context.annotation.Requires
import jakarta.inject.Singleton
import java.io.File
import java.time.Duration
import net.logstash.logback.marker.Markers.append
import org.slf4j.LoggerFactory

@Singleton
@Requires(notEnv = ["test", "staging"])
class FaceTecSearchAdapter(
    private val faceTecSDK: FaceTecSDKInstance,
    @Property(name = "integrations.facetec.search.databaseDirectoryPath") private val searchDatabaseDirectoryPath: String,
    private val authConfigurations: AuthConfig,
) : FaceMapSearchAdapter {
    private val searchGroups = HashMap<String, SearchGroup>()

    private var initialized = false
    private var initializationRetry = 0

    private fun initialize() {
        if (initialized) {
            return
        }
        initializationRetry++

        try {
            val initLoadTime = getZonedDateTime().toInstant()

            val existingSearch3D3DDatabaseDirectories =
                File(searchDatabaseDirectoryPath).listFiles { obj: File -> obj.isDirectory }

            if (existingSearch3D3DDatabaseDirectories.isEmpty()) {
                logger.warn(
                    append("info", "search database is empty"),
                    "FaceTecSearchAdapter",
                )
            }

            for (directory in existingSearch3D3DDatabaseDirectories) {
                val searchGroupName = directory.name
                val searchGroupKey = searchGroupName + FaceTecSearchType.TYPE_3D_3D
                val currentGroup =
                    faceTecSDK.create3D3DPersistedSearchGroup("$searchDatabaseDirectoryPath/$searchGroupName")
                searchGroups[searchGroupKey] = currentGroup
                logger.info(
                    append("searchGroupName", searchGroupName).andAppend("size", currentGroup.identifiers?.size),
                    "FaceTecSearchAdapter",
                )
            }

            val finishLoadTime = getZonedDateTime().toInstant()

            logger.info(
                append("databaseLoadTimeInSeconds", Duration.between(initLoadTime, finishLoadTime).toSeconds()),
                "FaceTecSearchAdapter",
            )
            initialized = true
        } catch (e: FaceTecPersistedSearchException) {
            if (e.error == FaceTecPersistedSearchException.Error.FILE_ACCESS) {
                logger.info(append("initializationRetry", initializationRetry), "FaceTecSearchAdapter", e)
            } else {
                logger.error(append("initializationRetry", initializationRetry), "FaceTecSearchAdapter", e)
            }
            throw e
        }
    }

    override suspend fun saveFaceMap(
        externalId: ExternalId,
        faceMap: ByteArray,
        group: FaceMatchGroup,
        clientId: LivenessClientId,
    ) {
        initialize()
        try {
            val searchGroupFolder = getSearchGroupKeyPath(clientId, group)
            val searchGroupKey = searchGroupFolder + FaceTecSearchType.TYPE_3D_3D
            val searchGroup = if (searchGroups[searchGroupKey] == null) {
                val created = faceTecSDK.create3D3DPersistedSearchGroup("$searchDatabaseDirectoryPath/$searchGroupFolder")
                searchGroups[searchGroupKey] = created
                created
            } else {
                searchGroups[searchGroupKey] as FaceTec3D3DSearchGroup
            }
            searchGroup.enroll3D_3D_DB(externalId.value, faceMap)
        } catch (ex: Exception) {
            logger.error(append("ACTION", "VERIFY"), "FaceTecSearchAdapter#saveFaceMap", ex)
        }
    }

    override suspend fun search3D(
        faceMap: ByteArray,
        group: FaceMatchGroup,
        clientId: LivenessClientId,
    ): Either<Exception, List<ExternalId>> {
        initialize()

        val searchGroupFolder = getSearchGroupKeyPath(clientId, group)
        val searchGroupKey = searchGroupFolder + FaceTecSearchType.TYPE_3D_3D
        val searchGroup = searchGroups[searchGroupKey] as FaceTec3D3DSearchGroup?

        if (searchGroup == null) {
            logger.warn(
                append("info", "search group not found").andAppend("searchGroupKey", searchGroupKey),
                "FaceTecSearchAdapter#search3D",
            )
            return emptyList<ExternalId>().right()
        }

        return searchGroup.search3D_3D_DB(
            faceMap,
            FaceTecMatchLevel.LEVEL_15,
        ).searchResults.map {
            logger.info(
                append("FaceTecMatchLevel", FaceTecMatchLevel.LEVEL_15).andAppend("result", it),
                "FaceTecSearchAdapter#search3D",
            )
            ExternalId(it.externalDatabaseRefID)
        }.right()
    }

    private fun getSearchGroupKeyPath(
        clientId: LivenessClientId,
        group: FaceMatchGroup,
    ): String {
        val tenantName = authConfigurations.tenantName(clientId)

        return if (tenantName.equals("friday", ignoreCase = true) || group == FaceMatchGroup.FRAUD_USERS) {
            return group.name
        } else {
            tenantName + group.name
        }
    }

    companion object {
        private val logger = LoggerFactory.getLogger(FaceTecSearchAdapter::class.java)
    }
}

enum class FaceTecSearchType { TYPE_3D_3D }