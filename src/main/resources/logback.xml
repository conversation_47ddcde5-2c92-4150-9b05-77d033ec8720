<configuration>
    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <withJansi>true</withJansi>
        <encoder class="net.logstash.logback.encoder.LogstashEncoder">
            <jsonGeneratorDecorator class="net.logstash.logback.mask.MaskingJsonGeneratorDecorator">
                <defaultMask>****</defaultMask>
                <path>CardNumber</path>
                <path>SecurityCode</path>
                <path>pan</path>
                <path>tokenusuario</path>
                <path>scanResultBlob</path>
                <path>auditTrailImage</path>
                <path>lowQualityAuditTrailImage</path>
                <path>faceScan</path>
                <path>auditImage</path>
                <path>faceMap</path>
                <path>continuousLearningFaceMap</path>
                <path>resultBlob</path>
            </jsonGeneratorDecorator>
        </encoder>
    </appender>
    <root level="info">
        <appender-ref ref="STDOUT"/>
    </root>
</configuration>
