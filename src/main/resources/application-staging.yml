integrations:
  facetec:
    host: https://api.facetec.com
    checkLivenessUrlPath: /api/v3.1/biometrics/liveness-3d
    createSessionUrlPath: /api/v3.1/biometrics/session-token
    client:
      productionKeyText: ""
liveness-gateway-auth:
  username: LIVENESS_CLIENT_ID-f96298e0-a338-11ed-8b23-4f406dbefac8
  password: STG_ENVIRONMENT_PASSWORD
features:
  oneToManySearch: false

application:
  accountNumber: ************
  region: us-east-1

auth:
  tenants:
    me-poupe:
      main:
        username: LIVENESS_CLIENT_ID-f96298e0-a338-11ed-8b23-4f406dbefac8
        password: STG_ENVIRONMENT_PASSWORD
      backoffice:
        username: LIVENESS_CLIENT_ID-f96298e0-a338-11ed-8b23-4f406dbefac8
        password: STG_ENVIRONMENT_PASSWORD
    friday:
      main:
        username: LIVENESS_CLIENT_ID-f96298e0-a338-11ed-8b23-4f406dbefac8
        password: STG_ENVIRONMENT_PASSWORD
      backoffice:
        username: LIVENESS_CLIENT_ID-f96298e0-a338-11ed-8b23-4f406dbefac8
        password: STG_ENVIRONMENT_PASSWORD