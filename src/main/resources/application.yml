micronaut:
  object-storage:
    aws:
      default:
        bucket: friday-facetec-documents-stg
  router:
    versioning:
      enabled: true
      default-version: 1
      header:
        enabled: true
        names:
          - 'X-API-VERSION'
  application:
    region: us-east-1
    name: liveness-gateway
  http:
    client:
      read-timeout: 10m
  #    server:
  #      read-timeout: 10m
  server:
    read-timeout: 600
    #    log-handled-exceptions: true
    ssl:
      enabled: true
      buildSelfSigned: true
    max-request-size: 52428800 #1024L * 1024 * 50
    netty:
      worker:
        event-loop-group: other
    cors:
      single-header: true
      enabled: true
      #    configurations:
      #      web:
      #        allowedOrigins:
      #          - https://use.via1.app(|.)
      #          - https://use.friday.ai(|.)
  netty:
    event-loops:
      other:
        num-threads: 10

endpoints:
  health:
    enabled: true
    sensitive: false
    details-visible: ANONYMOUS
    disk-space:
      enabled: false
  metrics:
    enabled: true
    sensitive: false

netty:
  default:
    allocator:
      max-order: 3

tracing:
  zipkin:
    enabled: true

integrations:
  facetec:
    sdk:
      deviceKeyIdentifier: "FROM_AWS_SECRETS"
      serverKey: "FROM_AWS_SECRETS"
      productionKeyText: "FROM_AWS_SECRETS"
      faceMapEncryptionKey: "FROM_AWS_SECRETS"
      usageLogsServerUri: "http://localhost:3000/"
    client:
      deviceKeyIdentifier: "FROM_AWS_SECRETS"
      productionKeyText: "FROM_AWS_SECRETS"
      publicFaceScanEncryptionKey: "FROM_AWS_SECRETS"
    search:
      databaseDirectoryPath: "/search3d3d"


auth:
  tenants:
    stress-test:
      allow-replay: true
      main:
        username: LIVENESS_CLIENT_ID-aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa
        password: LIVENESS_CLIENT_ID-STRESS_TEST_MAIN
      backoffice:
        username: LIVENESS_CLIENT_ID-bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb
        password: LIVENESS_CLIENT_ID-STRESS_TEST_BACKOFFICE
    me-poupe:
      main:
        username: LIVENESS_CLIENT_ID-418814a0-5db3-4f9e-b2f6-b6377018fb5e
        password: Liveness_gateway_mp_pwd_prod_!@#_to_be_replaced
      backoffice:
        username: LIVENESS_CLIENT_ID-a6bd4e23-39d0-4bcc-995c-c46d29d3c05b
        password: Liveness_gateway_mp_pwd_prod_!@#_to_be_replaced
    friday:
      main:
        username: LIVENESS_CLIENT_ID-271c3477-e22d-47f3-af7d-aaf95ac18b1a
        password: Liveness_gateway_pwd_prod_!@#_to_be_replaced
      backoffice:
        username: LIVENESS_CLIENT_ID-2402768b-8697-4cd6-ac94-ea078066e7e5
        password: Liveness_gateway_pwd_prod_!@#_to_be_replaced

dynamodb:
  region: us-east-1

features:
  enable-data-collection: false
  oneToManySearch: true
  allowedExternalIdPrefix: "DUPLICATED_CHECK"

facetec:
  s3:
    region: ${application.region:us-east-1}
    bucket: ${micronaut.object-storage.aws.default.bucket}

aws:
  region: ${application.region}
  sqs:
    publisher:
      enrollment-verify-duplication:
        queueName: enrollment-verify-duplication
    handler:
      enrollment-verify-duplication:
        queueName: ${aws.sqs.publisher.enrollment-verify-duplication.queueName}
        enabled: true
        consumers: 1
    sqsWaitTime: 20
    visibilityTimeout: 300
    maxNumberOfMessages: 10
    dlqArn: arn:aws:sqs:${application.region}:${application.accountNumber}:bill_events_dlq
    sqsCoolDownTime: 20