<configuration>
    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <withJansi>true</withJansi>
        <encoder class="net.logstash.logback.encoder.LogstashEncoder">
            <jsonGeneratorDecorator class="net.logstash.logback.mask.MaskingJsonGeneratorDecorator">
                <defaultMask>****</defaultMask>
                <path>CardNumber</path>
                <path>SecurityCode</path>
                <path>pan</path>
                <path>tokenusuario</path>
            </jsonGeneratorDecorator>
        </encoder>
    </appender>
    <root level="error">
        <appender-ref ref="STDOUT"/>
    </root>
    <logger name="io.micronaut.http.client" level="OFF"/>
</configuration>
