micronaut:
  otel:
    enabled: false
  server:
    ssl:
      enabled: false
    port: -1
    max-request-size: 10485760 #1024L*1024*10=>10MB
    multipart:
      enabled: true
      disk: false
      mixed: false
      max-file-size: 10485760
  application:
    name: liveness-gateway

aws:
  sdk:
    httpClientType: URL_CONNECTION

auth:
  tenants:
    me-poupe:
      main:
        username: "LIVENESS_CLIENT_ID-aaaaaaaa-bbbb-cccc-dddd-eeeeeeeeeeeb"
        password: "senha-correta"
      backoffice:
        username: "LIVENESS_CLIENT_ID-aaaaaaaa-bbbb-cccc-dddd-eeeeeeeeeeec"
        password: "senha-correta"
    friday:
      main:
        username: "LIVENESS_CLIENT_ID-aaaaaaaa-bbbb-cccc-dddd-eeeeeeeeeeee"
        password: "senha-correta"
      backoffice:
        username: "LIVENESS_CLIENT_ID-aaaaaaaa-bbbb-cccc-dddd-eeeeeeeee<PERSON>"
        password: "senha-correta"