package ai.friday.liveness

import ai.friday.liveness.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.liveness.adapters.api.AutidImageTO
import ai.friday.liveness.adapters.dynamo.LivenessEventDynamoDbRepository
import ai.friday.liveness.adapters.facetech.FaceTecSDKConfig
import ai.friday.liveness.adapters.micronaut.FeaturesConfigurationMicronaut
import ai.friday.liveness.app.LivenessClientId
import ai.friday.liveness.app.integrations.MessagePublisher
import ai.friday.liveness.app.liveness.AgeEstimateGroup
import ai.friday.liveness.app.liveness.EnrollmentCreated
import ai.friday.liveness.app.liveness.ExternalId
import ai.friday.liveness.app.liveness.FaceMapSearchAdapter
import ai.friday.liveness.app.liveness.FaceScanReceived
import ai.friday.liveness.app.liveness.FaceVerified
import ai.friday.liveness.app.liveness.FeatureConfiguration
import ai.friday.liveness.app.liveness.LivenessAdapter
import ai.friday.liveness.app.liveness.LivenessCheckResult
import ai.friday.liveness.app.liveness.LivenessEventRepository
import ai.friday.liveness.app.liveness.LivenessId
import ai.friday.liveness.app.liveness.LivenessSecurityCheck
import ai.friday.liveness.app.liveness.PlatformType
import ai.friday.liveness.app.liveness.SessionCreated
import ai.friday.liveness.app.session.SessionAdapter
import ai.friday.liveness.app.session.SessionTokenResult
import ai.friday.liveness.dynamo.DynamoDBUtils
import arrow.core.right
import io.kotest.core.spec.style.DescribeSpec
import io.kotest.matchers.shouldBe
import io.micronaut.core.type.Argument
import io.micronaut.http.HttpRequest
import io.micronaut.http.HttpStatus
import io.micronaut.http.client.HttpClient
import io.micronaut.http.client.exceptions.HttpClientResponseException
import io.micronaut.runtime.server.EmbeddedServer
import io.micronaut.test.annotation.MockBean
import io.micronaut.test.extensions.kotest.annotation.MicronautTest
import io.mockk.coEvery
import io.mockk.mockk
import java.util.UUID
import kotlinx.coroutines.reactive.awaitFirst
import org.junit.jupiter.api.assertThrows

private const val AUDIT_IMAGE_DATA = "AUDIT_IMAGE_DATA"

@MicronautTest
internal class InternalLivenessControllerAuditImageIntegrationTest(private val server: EmbeddedServer) : DescribeSpec(
    {
        val livenessRepository = server.applicationContext.getBean(LivenessEventRepository::class.java)
        val client = server.applicationContext.createBean(HttpClient::class.java, server.url)

        describe("ao pedir para criar um liveness sem as credenciais") {
            val exception = assertThrows<HttpClientResponseException> {
                client.toBlocking().retrieve(buildAuditImage())
            }

            it("deve retornar unauthorized") {
                exception.status shouldBe HttpStatus.UNAUTHORIZED
            }
        }

        describe("ao pedir para criar um liveness com as credenciais") {
            val username = LivenessClientId("LIVENESS_CLIENT_ID-aaaaaaaa-bbbb-cccc-dddd-eeeeeeeeeeee").value

            describe("e as credenciais são inválidas") {
                val password = "senha-errada"

                val exception = assertThrows<HttpClientResponseException> {
                    client.retrieve(buildAuditImage().basicAuth(username, password)).awaitFirst()
                }

                it("deve retornar unauthorized") {
                    exception.status shouldBe HttpStatus.UNAUTHORIZED
                }
            }

            describe("e as credenciais são válidas") {
                val password = "senha-correta"

                beforeContainer { DynamoDBUtils.cleanDynamoDB() }

                describe("se liveness está mal formatado") {
                    val thrown = assertThrows<HttpClientResponseException> {
                        client.exchange(
                            buildAuditImage("ID_MAL_FORMATADO").basicAuth(username, password),
                            Argument.STRING,
                        ).awaitFirst()
                    }

                    it("deve retornar bad request") {
                        thrown.status shouldBe HttpStatus.BAD_REQUEST
                    }
                }

                describe("se liveness não existe") {
                    val thrown = assertThrows<HttpClientResponseException> {
                        client.exchange(
                            buildAuditImage().basicAuth(username, password),
                            Argument.STRING,
                        ).awaitFirst()
                    }

                    it("deve retornar bad request") {
                        thrown.status shouldBe HttpStatus.BAD_REQUEST
                    }
                }

                describe("se o liveness existe") {
                    val livenessId = LivenessId.generate()

                    beforeContainer {
                        DynamoDBUtils.cleanDynamoDB()
                        livenessRepository.save(
                            EnrollmentCreated(
                                livenessId = livenessId,
                                created = getZonedDateTime(),
                                externalId = ExternalId("ACCOUNT_ID-${UUID.randomUUID()}"),
                                clientId = livenessClientIdFridayMain,
                            ),
                        )
                        livenessRepository.save(
                            SessionCreated(
                                livenessId = livenessId,
                                created = getZonedDateTime(),
                                deviceKeyIdentifier = "deviceKeyIdentifier",
                                userAgent = "userAgent",
                                clientIpAddress = "***********",
                            ),
                        )
                        livenessRepository.save(
                            FaceScanReceived(
                                livenessId = livenessId,
                                created = getZonedDateTime(),
                                faceScanDigest = "faceScanDigest",
                            ),
                        )
                    }

                    describe("se o liveness ainda não foi concluido") {
                        it("deve retornar bad request") {
                            val thrown = assertThrows<HttpClientResponseException> {
                                client.exchange(
                                    buildAuditImage(livenessId.value).basicAuth(username, password),
                                    Argument.STRING,
                                ).awaitFirst()
                            }
                            thrown.status shouldBe HttpStatus.BAD_REQUEST
                        }
                    }

                    describe("se o liveness falhou") {
                        beforeTest {
                            livenessRepository.save(
                                FaceVerified(
                                    livenessId = livenessId,
                                    created = getZonedDateTime(),
                                    success = false,
                                    ageEstimateGroup = AgeEstimateGroup.UNKNOWN,
                                    platformType = PlatformType.UNKNOWN,
                                    deviceSDKVersion = "deviceSDKVersion",
                                    scanResultBlob = "scanResultBlob",
                                    securityCheck = LivenessSecurityCheck(
                                        faceScanLivenessCheck = false,
                                        auditTrailVerificationCheck = false,
                                        sessionTokenCheck = false,
                                    ),
                                    faceMap = null,
                                    auditTrailImage = null,
                                ),
                            )
                        }
                        it("deve retornar bad request") {
                            val thrown = assertThrows<HttpClientResponseException> {
                                client.exchange(
                                    buildAuditImage(livenessId.value).basicAuth(username, password),
                                    Argument.STRING,
                                ).awaitFirst()
                            }
                            thrown.status shouldBe HttpStatus.BAD_REQUEST
                        }
                    }

                    describe("se o facemap for nulo") {
                        beforeTest {
                            livenessRepository.save(
                                FaceVerified(
                                    livenessId = livenessId,
                                    created = getZonedDateTime(),
                                    success = true,
                                    ageEstimateGroup = AgeEstimateGroup.OVER_THIRTY,
                                    platformType = PlatformType.IOS,
                                    deviceSDKVersion = "deviceSDKVersion",
                                    scanResultBlob = "scanResultBlob",
                                    securityCheck = LivenessSecurityCheck(
                                        faceScanLivenessCheck = true,
                                        auditTrailVerificationCheck = true,
                                        sessionTokenCheck = true,
                                    ),
                                    faceMap = null,
                                    auditTrailImage = null,
                                ),
                            )
                        }
                        it("deve retornar bad request") {
                            val thrown = assertThrows<HttpClientResponseException> {
                                client.exchange(
                                    buildAuditImage(livenessId.value).basicAuth(username, password),
                                    Argument.STRING,
                                ).awaitFirst()
                            }
                            thrown.status shouldBe HttpStatus.BAD_REQUEST
                        }
                    }

                    describe("se o facemap estiver com valor") {
                        beforeTest {
                            livenessRepository.save(
                                FaceVerified(
                                    livenessId = livenessId,
                                    created = getZonedDateTime(),
                                    success = true,
                                    ageEstimateGroup = AgeEstimateGroup.OVER_THIRTY,
                                    platformType = PlatformType.IOS,
                                    deviceSDKVersion = "deviceSDKVersion",
                                    scanResultBlob = "scanResultBlob",
                                    securityCheck = LivenessSecurityCheck(
                                        faceScanLivenessCheck = true,
                                        auditTrailVerificationCheck = true,
                                        sessionTokenCheck = true,
                                    ),
                                    faceMap = "faceMapContent",
                                    auditTrailImage = null,
                                ),
                            )
                        }
                        it("deve retornar o base64 da imagem") {
                            val response =
                                client.exchange(
                                    buildAuditImage(livenessId.value).basicAuth(username, password),
                                    Argument.of(AutidImageTO::class.java),
                                ).awaitFirst()

                            response.status shouldBe HttpStatus.OK
                            response.body()?.livenessId shouldBe livenessId.value
                            response.body()?.auditImage shouldBe "QVVESVRfSU1BR0VfREFUQQ=="
                        }
                    }

                    describe("se o facemap e o audit trail estiverem com valor") {
                        beforeTest {
                            livenessRepository.save(
                                FaceVerified(
                                    livenessId = livenessId,
                                    created = getZonedDateTime(),
                                    success = true,
                                    ageEstimateGroup = AgeEstimateGroup.OVER_THIRTY,
                                    platformType = PlatformType.IOS,
                                    deviceSDKVersion = "deviceSDKVersion",
                                    scanResultBlob = "scanResultBlob",
                                    securityCheck = LivenessSecurityCheck(
                                        faceScanLivenessCheck = true,
                                        auditTrailVerificationCheck = true,
                                        sessionTokenCheck = true,
                                    ),
                                    faceMap = "faceMapContent",
                                    auditTrailImage = "YXVkaXRUcmFpbEltYWdl",
                                ),
                            )
                        }
                        it("deve retornar o base64 do audit trail") {
                            val response =
                                client.exchange(
                                    buildAuditImage(livenessId.value).basicAuth(username, password),
                                    Argument.of(AutidImageTO::class.java),
                                ).awaitFirst()

                            response.status shouldBe HttpStatus.OK
                            response.body()?.livenessId shouldBe livenessId.value
                            response.body()?.auditImage shouldBe "YXVkaXRUcmFpbEltYWdl"
                        }
                    }
                }
            }
        }
    },
) {
    private val setupDynamoDB = DynamoDBUtils.setupDynamoDB()
    private val livenessEventDbrepository = LivenessEventDynamoDbRepository(setupDynamoDB)

    private val sessionAdapter: SessionAdapter = mockk {
        coEvery { createSession(any()) } returns SessionTokenResult(token = "123").right()
    }

    private val livenessAdapterMock: LivenessAdapter = mockk {
        coEvery { checkLiveness(any()) } returns LivenessCheckResult.createSuccessResult(
            ageEstimateGroup = AgeEstimateGroup.OVER_THIRTY,
            platformType = PlatformType.IOS,
            deviceSDKVersion = "deviceSDKVersion",
            securityCheck = LivenessSecurityCheck(
                sessionTokenCheck = true,
                auditTrailVerificationCheck = true,
                faceScanLivenessCheck = true,
            ),
            faceMap = ByteWrapper("FACE_MAP_FAKE".toByteArray()),
            scanResultBlobFactory = { "blob" },
        ).right()
        coEvery {
            extractAuditImageFromFacemap(any())
        } returns AUDIT_IMAGE_DATA.toByteArray().right()
    }

    private val faceMapSearchAdapterMock: FaceMapSearchAdapter = mockk()

    @MockBean(FaceTecSDKConfig::class)
    fun getFaceTecSDKConfig(): FaceTecSDKConfig = FaceTecSDKConfig().apply {
        deviceKeyIdentifier = ""
        serverKey = ""
        productionKeyText = ""
        faceMapEncryptionKey = ""
        usageLogsServerUri = ""
    }

    @MockBean(FeaturesConfigurationMicronaut::class)
    fun getFeaturesConfigurationMicronaut(): FeatureConfiguration = featureConfiguration
    private val featureConfiguration = mockk<FeatureConfiguration>(relaxed = true)

    @MockBean(SessionAdapter::class)
    fun getSessionAdapter(): SessionAdapter = sessionAdapter

    @MockBean(LivenessAdapter::class)
    fun getLivenessAdapter(): LivenessAdapter = livenessAdapterMock

    @MockBean(FaceMapSearchAdapter::class)
    fun getFaceMapSearchAdapter(): FaceMapSearchAdapter = faceMapSearchAdapterMock

    @MockBean(LivenessEventDynamoDbRepository::class)
    fun getLivenessEventRepository(): LivenessEventRepository = livenessEventDbrepository

    @MockBean(MessagePublisher::class)
    fun getMessagePublisher(): MessagePublisher = messagePublisher
    private val messagePublisher: MessagePublisher = mockk(relaxUnitFun = true)
}

private fun buildAuditImage(livenessId: String = LivenessId.generate().value) =
    HttpRequest.GET<AutidImageTO>("/internal/enrollment/$livenessId/auditImage")