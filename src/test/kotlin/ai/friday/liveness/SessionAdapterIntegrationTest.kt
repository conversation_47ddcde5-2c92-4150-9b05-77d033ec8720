package ai.friday.liveness

import ai.friday.liveness.app.session.OperationType
import ai.friday.liveness.app.session.SessionAdapter
import ai.friday.liveness.app.session.SessionTokenRequest
import io.kotest.core.annotation.Ignored
import io.kotest.core.spec.style.AnnotationSpec.Test
import io.kotest.matchers.shouldBe
import io.micronaut.test.extensions.kotest.annotation.MicronautTest
import kotlinx.coroutines.runBlocking

@Suppress("NonAsciiCharacters")
@MicronautTest
@Ignored
class SessionAdapterIntegrationTest(private val sessionAdapter: SessionAdapter) {

    @Test
    fun `criar sessão de liveness`() {
        val result = runBlocking {
            sessionAdapter.createSession(
                SessionTokenRequest(
                    deviceKeyIdentifier = "d8Shpkngoz7TfrJRPJJyfeShvgd0bsCU",
                    userAgent = "foo",
                    operationId = "1234",
                    type = OperationType.LIVENESS_CHECK,
                    clientIpAddress = "************",
                ),
            )
        }

        result.isRight() shouldBe true
    }
}