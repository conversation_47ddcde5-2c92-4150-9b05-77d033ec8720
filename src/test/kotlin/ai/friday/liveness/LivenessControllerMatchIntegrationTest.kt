package ai.friday.liveness

import ai.friday.liveness.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.liveness.app.liveness.AgeEstimateGroup
import ai.friday.liveness.app.liveness.CompletedLiveness
import ai.friday.liveness.app.liveness.EnrollmentCreated
import ai.friday.liveness.app.liveness.ExternalId
import ai.friday.liveness.app.liveness.FaceScanReceived
import ai.friday.liveness.app.liveness.FaceVerified
import ai.friday.liveness.app.liveness.LivenessCheckResult
import ai.friday.liveness.app.liveness.LivenessId
import ai.friday.liveness.app.liveness.LivenessSecurityCheck
import ai.friday.liveness.app.liveness.MatchCreated
import ai.friday.liveness.app.liveness.PlatformType
import ai.friday.liveness.app.liveness.SessionCreated
import arrow.core.right
import io.kotest.matchers.shouldBe
import io.kotest.matchers.types.shouldBeInstanceOf
import io.micronaut.http.HttpStatus
import io.micronaut.http.client.exceptions.HttpClientResponseException
import io.micronaut.runtime.server.EmbeddedServer
import io.micronaut.test.extensions.kotest.annotation.MicronautTest
import io.mockk.coEvery
import org.junit.jupiter.api.assertThrows

@MicronautTest
internal class LivenessControllerMatchIntegrationTest(
    embeddedServer: EmbeddedServer,
) : LivenessControllerIntegrationTest(
    embeddedServer,
    MatchCreated(
        livenessId = LivenessId.generate(),
        created = getZonedDateTime(),
        clientId = livenessClientIdFridayMain,
        externalId = ExternalId("ACCOUNT-1"),
    ),
) {

    private val enrollmentLivenessId = LivenessId.generate()

    init {
        describe("quando o liveness id usado existe e a sessão foi criada corretamente") {
            beforeEach {
                livenessDbRepository.save(
                    firstEvent,
                )
                client.createSession(livenessId)
            }

            describe("quando quando não há liveness de enrollment prévio completo") {
                it("deve retornar unauthorized") {
                    val exception = assertThrows<HttpClientResponseException> {
                        client.checkLiveness(livenessId)
                    }

                    exception.status shouldBe HttpStatus.UNAUTHORIZED
                }
            }

            describe("quando há liveness de enrollment prévio completo com facemap null") {
                beforeEach {
                    prepareDatabaseToMakeInitialValidationPass(null)
                }
                it("deve retornar unauthorized") {
                    val exception = assertThrows<HttpClientResponseException> {
                        client.checkLiveness(livenessId)
                    }

                    exception.status shouldBe HttpStatus.UNAUTHORIZED
                }
            }

            describe("quando ocorre a atualização do facemap devido ao aprendizado") {
                beforeEach {
                    prepareDatabaseToMakeInitialValidationPass()
                    prepareLivenessAdapterMock(
                        LivenessCheckResult.createSuccessResult(
                            ageEstimateGroup = AgeEstimateGroup.OVER_THIRTY,
                            platformType = PlatformType.IOS,
                            deviceSDKVersion = "deviceSDKVersion",
                            securityCheck = LivenessSecurityCheck(
                                sessionTokenCheck = true,
                                auditTrailVerificationCheck = true,
                                faceScanLivenessCheck = true,
                            ),
                            faceMap = ByteWrapper("QVRVQUxJWkFET19GQUNFX01BUF9GQUtF"),
                            scanResultBlobFactory = { "blob" },
                        ),
                    )
                }
                it("deve retornar ok e atualizar o faceMap do liveness do enrollmentOriginal") {
                    with(client.checkLiveness(livenessId)) {
                        status shouldBe HttpStatus.OK
                    }

                    val enrollmentLiveness = livenessDbRepository.findByIdOrNull(enrollmentLivenessId)
                    enrollmentLiveness.shouldBeInstanceOf<CompletedLiveness>()
                    enrollmentLiveness.faceMap!!.getBase64() shouldBe "QVRVQUxJWkFET19GQUNFX01BUF9GQUtF"
                }
            }
        }
    }

    override suspend fun prepareDatabaseToMakeInitialValidationPass(faceMap: String?) {
        livenessDbRepository.save(
            EnrollmentCreated(
                livenessId = enrollmentLivenessId,
                created = getZonedDateTime(),
                externalId = (firstEvent as MatchCreated).externalId,
                clientId = livenessClientId,
            ),
        )
        livenessDbRepository.save(
            SessionCreated(
                livenessId = enrollmentLivenessId,
                created = getZonedDateTime(),
                deviceKeyIdentifier = "deviceKeyIdentifier",
                userAgent = "userAgent",
                clientIpAddress = "***********",
            ),
        )

        livenessDbRepository.save(
            FaceScanReceived(
                livenessId = enrollmentLivenessId,
                created = getZonedDateTime(),
                faceScanDigest = "faceScanDigest",
            ),
        )

        livenessDbRepository.save(
            FaceVerified(
                livenessId = enrollmentLivenessId,
                created = getZonedDateTime(),
                success = true,
                ageEstimateGroup = AgeEstimateGroup.OVER_THIRTY,
                platformType = PlatformType.IOS,
                deviceSDKVersion = "deviceSDKVersion",
                scanResultBlob = "scanResultBlob",
                securityCheck = LivenessSecurityCheck(
                    faceScanLivenessCheck = true,
                    auditTrailVerificationCheck = true,
                    sessionTokenCheck = true,
                ),
                faceMap = faceMap,
                auditTrailImage = null,
            ),
        )
    }

    override fun prepareLivenessAdapterMock(result: LivenessCheckResult) {
        coEvery {
            livenessAdapterMock.matchLiveness(any(), any())
        } returns result.right()
    }
}