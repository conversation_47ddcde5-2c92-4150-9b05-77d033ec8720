package ai.friday.liveness.adapters.api

import ai.friday.liveness.app.LivenessClientId
import ai.friday.liveness.app.auth.AuthConfig
import ai.friday.liveness.app.liveness.LivenessProvider
import ai.friday.liveness.app.liveness.tenantProvider
import io.kotest.core.spec.style.DescribeSpec
import io.kotest.matchers.shouldBe
import io.micronaut.test.extensions.kotest.annotation.MicronautTest

@MicronautTest
internal class InternalLivenessControllerProviderTest(
    private val authConfig: AuthConfig,
) : DescribeSpec({

    describe("Provider determination from client configuration") {

        describe("when getting provider for me-poupe client") {
            val clientId = LivenessClientId("LIVENESS_CLIENT_ID-aaaaaaaa-bbbb-cccc-dddd-eeeeeeeeeeeb")

            it("should return facetech provider from configuration") {
                val provider = authConfig.tenantProvider(clientId)
                provider shouldBe LivenessProvider.FACETECH
            }
        }

        describe("when getting provider for friday client") {
            val clientId = LivenessClientId("LIVENESS_CLIENT_ID-aaaaaaaa-bbbb-cccc-dddd-eeeeeeeeeeee")

            it("should return facetech provider from configuration") {
                val provider = authConfig.tenantProvider(clientId)
                provider shouldBe LivenessProvider.FACETECH
            }
        }

        describe("when getting provider for friday backoffice client") {
            val clientId = LivenessClientId("LIVENESS_CLIENT_ID-aaaaaaaa-bbbb-cccc-dddd-eeeeeeeeeeed")

            it("should return facetech provider from configuration") {
                val provider = authConfig.tenantProvider(clientId)
                provider shouldBe LivenessProvider.FACETECH
            }
        }
    }
})