package ai.friday.liveness.adapters.api

import ai.friday.liveness.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.liveness.ByteWrapper
import ai.friday.liveness.adapters.dynamo.EXTERNAL_ID
import ai.friday.liveness.adapters.dynamo.LivenessEventDynamoDbRepository
import ai.friday.liveness.adapters.facetech.FaceTecSDKConfig
import ai.friday.liveness.adapters.micronaut.FeaturesConfigurationMicronaut
import ai.friday.liveness.app.LivenessClientId
import ai.friday.liveness.app.integrations.MessagePublisher
import ai.friday.liveness.app.liveness.AgeEstimateGroup
import ai.friday.liveness.app.liveness.EnrollmentCreated
import ai.friday.liveness.app.liveness.ExternalId
import ai.friday.liveness.app.liveness.FaceMapSearchAdapter
import ai.friday.liveness.app.liveness.FaceVerified
import ai.friday.liveness.app.liveness.FeatureConfiguration
import ai.friday.liveness.app.liveness.LivenessAdapter
import ai.friday.liveness.app.liveness.LivenessCheckResult
import ai.friday.liveness.app.liveness.LivenessEventRepository
import ai.friday.liveness.app.liveness.LivenessId
import ai.friday.liveness.app.liveness.LivenessSecurityCheck
import ai.friday.liveness.app.liveness.MatchCreated
import ai.friday.liveness.app.liveness.PlatformType
import ai.friday.liveness.app.liveness.SessionCreated
import ai.friday.liveness.app.session.SessionAdapter
import ai.friday.liveness.app.session.SessionTokenResult
import ai.friday.liveness.dynamo.DynamoDBUtils
import ai.friday.liveness.livenessClientIdFridayMain
import arrow.core.right
import io.kotest.core.spec.style.DescribeSpec
import io.kotest.matchers.shouldBe
import io.micronaut.core.type.Argument
import io.micronaut.http.HttpRequest
import io.micronaut.http.HttpResponse
import io.micronaut.http.HttpStatus
import io.micronaut.http.client.HttpClient
import io.micronaut.http.client.exceptions.HttpClientResponseException
import io.micronaut.runtime.server.EmbeddedServer
import io.micronaut.test.annotation.MockBean
import io.micronaut.test.extensions.kotest.annotation.MicronautTest
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.reactive.awaitFirst
import org.junit.jupiter.api.assertThrows

@MicronautTest
internal class InternalLivenessControllerMatchIntegrationTest(server: EmbeddedServer) :
    DescribeSpec() {
    private val livenessDbRepository: LivenessEventRepository =
        server.applicationContext.getBean(LivenessEventRepository::class.java)
    private val client: HttpClient = server.applicationContext.createBean(HttpClient::class.java, server.url)

    private val setupDynamoDB = DynamoDBUtils.setupDynamoDB()
    private val livenessEventDbRepository = LivenessEventDynamoDbRepository(setupDynamoDB)

    private val sessionAdapter: SessionAdapter = mockk {
        coEvery { createSession(any()) } returns SessionTokenResult(token = "123").right()
    }

    private val livenessAdapterMock: LivenessAdapter = mockk {
        coEvery { checkLiveness(any()) } returns LivenessCheckResult.createSuccessResult(
            ageEstimateGroup = AgeEstimateGroup.OVER_THIRTY,
            platformType = PlatformType.IOS,
            deviceSDKVersion = "deviceSDKVersion",
            securityCheck = LivenessSecurityCheck(
                sessionTokenCheck = true,
                auditTrailVerificationCheck = true,
                faceScanLivenessCheck = true,
            ),
            scanResultBlobFactory = { "blob" },
            faceMap = ByteWrapper("FACE_MAP_FAKE".toByteArray()),
        ).right()
    }

    private val faceMapSearchAdapterMock: FaceMapSearchAdapter = mockk()

    @MockBean(FeaturesConfigurationMicronaut::class)
    fun getFeaturesConfigurationMicronaut(): FeatureConfiguration = featureConfiguration
    private val featureConfiguration = mockk<FeatureConfiguration>(relaxed = true)

    @MockBean(FaceTecSDKConfig::class)
    fun getFaceTecSDKConfig(): FaceTecSDKConfig = FaceTecSDKConfig().apply {
        deviceKeyIdentifier = ""
        serverKey = ""
        productionKeyText = ""
        faceMapEncryptionKey = ""
        usageLogsServerUri = ""
    }

    @MockBean(SessionAdapter::class)
    fun getSessionAdapter(): SessionAdapter = sessionAdapter

    @MockBean(LivenessAdapter::class)
    fun getLivenessAdapter(): LivenessAdapter = livenessAdapterMock

    @MockBean(FaceMapSearchAdapter::class)
    fun getFaceMapSearchAdapter(): FaceMapSearchAdapter = faceMapSearchAdapterMock

    @MockBean(LivenessEventDynamoDbRepository::class)
    fun getLivenessEventRepository(): LivenessEventRepository = livenessEventDbRepository

    @MockBean(MessagePublisher::class)
    fun getMessagePublisher(): MessagePublisher = messagePublisher
    private val messagePublisher: MessagePublisher = mockk(relaxUnitFun = true)

    private val livenessId = LivenessId.generate()
    private val livenessClientId = livenessClientIdFridayMain
    private val externalId = ExternalId(EXTERNAL_ID)

    init {
        describe("ao consultar o match de um liveness que não existe") {
            beforeTest {
                DynamoDBUtils.cleanDynamoDB()
            }

            it("deve retornar que não deu match") {
                val response = assertThrows<HttpClientResponseException> { client.matchVerification(livenessId) }
                response.status shouldBe HttpStatus.BAD_REQUEST
            }
        }

        describe("ao consultar o match de um liveness que não seja do tipo MATCH") {
            beforeTest {
                DynamoDBUtils.cleanDynamoDB()

                livenessDbRepository.save(
                    EnrollmentCreated(
                        livenessId = livenessId,
                        created = getZonedDateTime(),
                        clientId = livenessClientIdFridayMain,
                        externalId = externalId,
                    ),
                )
            }

            it("deve retornar que não deu match") {
                val response = assertThrows<HttpClientResponseException> { client.matchVerification(livenessId) }
                response.status shouldBe HttpStatus.BAD_REQUEST
            }
        }

        describe("ao consultar o match de um liveness que seja do tipo MATCH") {
            beforeTest {
                DynamoDBUtils.cleanDynamoDB()

                livenessDbRepository.save(
                    MatchCreated(
                        livenessId = livenessId,
                        created = getZonedDateTime(),
                        clientId = livenessClientIdFridayMain,
                        externalId = externalId,
                    ),
                )
            }

            describe("quando não criou a sessão") {
                it("deve retornar que não deu match") {
                    val response = client.matchVerification(livenessId)
                    response.status shouldBe HttpStatus.OK
                    response.body() shouldBe MatchVerificationResponseTO(
                        livenessId = livenessId.value,
                        externalId = externalId.value,
                        match = false,
                        attempt = 0,
                    )
                }
            }

            describe("quando criou a sessão") {
                beforeTest {
                    livenessDbRepository.save(
                        SessionCreated(
                            livenessId = livenessId,
                            created = getZonedDateTime(),
                            deviceKeyIdentifier = "deviceKeyIdentifier",
                            userAgent = "userAgent",
                            clientIpAddress = "clientIpAddress",
                        ),
                    )
                }

                it("deve retornar que não deu match") {
                    val response = client.matchVerification(livenessId)
                    response.status shouldBe HttpStatus.OK
                    response.body() shouldBe MatchVerificationResponseTO(
                        livenessId = livenessId.value,
                        externalId = externalId.value,
                        match = false,
                        attempt = 0,
                    )
                }

                describe("quando usuario fez uma tentativa que falhou") {
                    beforeTest {
                        livenessDbRepository.save(
                            FaceVerified(
                                livenessId = livenessId,
                                created = getZonedDateTime(),
                                success = false,
                                ageEstimateGroup = AgeEstimateGroup.UNKNOWN,
                                platformType = PlatformType.UNKNOWN,
                                deviceSDKVersion = "deviceSDKVersion",
                                scanResultBlob = "scanResultBlob",
                                securityCheck = LivenessSecurityCheck(
                                    faceScanLivenessCheck = false,
                                    auditTrailVerificationCheck = false,
                                    sessionTokenCheck = false,
                                ),
                                faceMap = null,
                                auditTrailImage = null,
                            ),
                        )
                    }

                    it("deve retornar que não deu match com uma tentativa") {
                        val response = client.matchVerification(livenessId)
                        response.status shouldBe HttpStatus.OK
                        response.body() shouldBe MatchVerificationResponseTO(
                            livenessId = livenessId.value,
                            externalId = externalId.value,
                            match = false,
                            attempt = 1,
                        )
                    }

                    describe("quando usuario fez uma nova tentativa que funcionou") {
                        beforeTest {
                            livenessDbRepository.save(
                                FaceVerified(
                                    livenessId = livenessId,
                                    created = getZonedDateTime(),
                                    success = true,
                                    ageEstimateGroup = AgeEstimateGroup.UNKNOWN,
                                    platformType = PlatformType.UNKNOWN,
                                    deviceSDKVersion = "deviceSDKVersion",
                                    scanResultBlob = "scanResultBlob",
                                    securityCheck = LivenessSecurityCheck(
                                        faceScanLivenessCheck = true,
                                        auditTrailVerificationCheck = true,
                                        sessionTokenCheck = true,
                                    ),
                                    faceMap = ByteWrapper("FACE_MAP".toByteArray()).getBase64(),
                                    auditTrailImage = ByteWrapper("auditTrailImage".toByteArray()).getBase64(),
                                ),
                            )
                        }

                        it("deve retornar que deu match com uma tentativa") {
                            val response = client.matchVerification(livenessId)
                            response.status shouldBe HttpStatus.OK
                            response.body() shouldBe MatchVerificationResponseTO(
                                livenessId = livenessId.value,
                                externalId = externalId.value,
                                match = true,
                                attempt = 2,
                            )
                        }
                    }
                }

                describe("quando usuario fez uma tentativa que funcionou") {
                    beforeTest {
                        livenessDbRepository.save(
                            FaceVerified(
                                livenessId = livenessId,
                                created = getZonedDateTime(),
                                success = true,
                                ageEstimateGroup = AgeEstimateGroup.UNKNOWN,
                                platformType = PlatformType.UNKNOWN,
                                deviceSDKVersion = "deviceSDKVersion",
                                scanResultBlob = "scanResultBlob",
                                securityCheck = LivenessSecurityCheck(
                                    faceScanLivenessCheck = true,
                                    auditTrailVerificationCheck = true,
                                    sessionTokenCheck = true,
                                ),
                                faceMap = ByteWrapper("FACE_MAP".toByteArray()).getBase64(),
                                auditTrailImage = ByteWrapper("auditTrailImage".toByteArray()).getBase64(),
                            ),
                        )
                    }

                    it("deve retornar que deu match com uma tentativa") {
                        val response = client.matchVerification(livenessId)
                        response.status shouldBe HttpStatus.OK
                        response.body() shouldBe MatchVerificationResponseTO(
                            livenessId = livenessId.value,
                            externalId = externalId.value,
                            match = true,
                            attempt = 1,
                        )
                    }
                }
            }
        }
    }
}

private suspend fun HttpClient.matchVerification(livenessId: LivenessId): HttpResponse<MatchVerificationResponseTO> {
    val password = "senha-correta"
    val username = LivenessClientId("LIVENESS_CLIENT_ID-aaaaaaaa-bbbb-cccc-dddd-eeeeeeeeeeee").value
    return this.exchange(
        HttpRequest.GET<EnrollmentVerificationTO>("/internal/match/validated/${livenessId.value}")
            .basicAuth(username, password),
        Argument.of(MatchVerificationResponseTO::class.java),
    ).awaitFirst()
}