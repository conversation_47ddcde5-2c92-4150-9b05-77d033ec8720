package ai.friday.liveness.adapters.api

import ai.friday.liveness.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.liveness.ByteWrapper
import ai.friday.liveness.adapters.dynamo.EXTERNAL_ID
import ai.friday.liveness.adapters.dynamo.LivenessEventDynamoDbRepository
import ai.friday.liveness.adapters.facetech.FaceTecSDKConfig
import ai.friday.liveness.adapters.micronaut.FeaturesConfigurationMicronaut
import ai.friday.liveness.app.LivenessClientId
import ai.friday.liveness.app.integrations.MessagePublisher
import ai.friday.liveness.app.liveness.AgeEstimateGroup
import ai.friday.liveness.app.liveness.DuplicationVerified
import ai.friday.liveness.app.liveness.EnrollmentCreated
import ai.friday.liveness.app.liveness.ExternalId
import ai.friday.liveness.app.liveness.FaceMapSearchAdapter
import ai.friday.liveness.app.liveness.FaceScanReceived
import ai.friday.liveness.app.liveness.FaceVerified
import ai.friday.liveness.app.liveness.FeatureConfiguration
import ai.friday.liveness.app.liveness.FraudVerified
import ai.friday.liveness.app.liveness.LivenessAdapter
import ai.friday.liveness.app.liveness.LivenessCheckResult
import ai.friday.liveness.app.liveness.LivenessEventRepository
import ai.friday.liveness.app.liveness.LivenessId
import ai.friday.liveness.app.liveness.LivenessSecurityCheck
import ai.friday.liveness.app.liveness.PlatformType
import ai.friday.liveness.app.liveness.SessionCreated
import ai.friday.liveness.app.session.SessionAdapter
import ai.friday.liveness.app.session.SessionTokenResult
import ai.friday.liveness.dynamo.DynamoDBUtils
import ai.friday.liveness.livenessClientIdFridayMain
import arrow.core.right
import io.kotest.core.spec.style.DescribeSpec
import io.kotest.matchers.shouldBe
import io.micronaut.core.type.Argument
import io.micronaut.http.HttpRequest
import io.micronaut.http.HttpResponse
import io.micronaut.http.HttpStatus
import io.micronaut.http.client.HttpClient
import io.micronaut.http.client.exceptions.HttpClientResponseException
import io.micronaut.runtime.server.EmbeddedServer
import io.micronaut.test.annotation.MockBean
import io.micronaut.test.extensions.kotest.annotation.MicronautTest
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.reactive.awaitFirst

@MicronautTest
internal class InternalLivenessControllerEnrollmentVerificationIntegrationTest(private val server: EmbeddedServer) :
    DescribeSpec() {
    private val livenessDbRepository: LivenessEventRepository =
        server.applicationContext.getBean(LivenessEventRepository::class.java)
    private val client: HttpClient = server.applicationContext.createBean(HttpClient::class.java, server.url)

    private val setupDynamoDB = DynamoDBUtils.setupDynamoDB()
    private val livenessEventDbrepository = LivenessEventDynamoDbRepository(setupDynamoDB)

    private val sessionAdapter: SessionAdapter = mockk {
        coEvery { createSession(any()) } returns SessionTokenResult(token = "123").right()
    }

    private val livenessAdapterMock: LivenessAdapter = mockk {
        coEvery { checkLiveness(any()) } returns LivenessCheckResult.createSuccessResult(
            ageEstimateGroup = AgeEstimateGroup.OVER_THIRTY,
            platformType = PlatformType.IOS,
            deviceSDKVersion = "deviceSDKVersion",
            securityCheck = LivenessSecurityCheck(
                sessionTokenCheck = true,
                auditTrailVerificationCheck = true,
                faceScanLivenessCheck = true,
            ),
            scanResultBlobFactory = { "blob" },
            faceMap = ByteWrapper("FACE_MAP_FAKE".toByteArray()),
        ).right()
    }

    private val faceMapSearchAdapterMock: FaceMapSearchAdapter = mockk()

    @MockBean(FeaturesConfigurationMicronaut::class)
    fun getFeaturesConfigurationMicronaut(): FeatureConfiguration = featureConfiguration
    private val featureConfiguration = mockk<FeatureConfiguration>(relaxed = true)

    @MockBean(FaceTecSDKConfig::class)
    fun getFaceTecSDKConfig(): FaceTecSDKConfig = FaceTecSDKConfig().apply {
        deviceKeyIdentifier = ""
        serverKey = ""
        productionKeyText = ""
        faceMapEncryptionKey = ""
        usageLogsServerUri = ""
    }

    @MockBean(SessionAdapter::class)
    fun getSessionAdapter(): SessionAdapter = sessionAdapter

    @MockBean(LivenessAdapter::class)
    fun getLivenessAdapter(): LivenessAdapter = livenessAdapterMock

    @MockBean(FaceMapSearchAdapter::class)
    fun getFaceMapSearchAdapter(): FaceMapSearchAdapter = faceMapSearchAdapterMock

    @MockBean(LivenessEventDynamoDbRepository::class)
    fun getLivenessEventRepository(): LivenessEventRepository = livenessEventDbrepository

    @MockBean(MessagePublisher::class)
    fun getMessagePublisher(): MessagePublisher = messagePublisher
    private val messagePublisher: MessagePublisher = mockk(relaxUnitFun = true)

    init {
        describe("ao consultar a duplicação do rosto de uma pessoa") {
            val livenessId = LivenessId.generate()
            val livenessClientId = livenessClientIdFridayMain

            val externalId = ExternalId(EXTERNAL_ID)

            beforeContainer {

                DynamoDBUtils.cleanDynamoDB()

                livenessDbRepository.save(
                    EnrollmentCreated(
                        livenessId = livenessId,
                        created = getZonedDateTime(),
                        externalId = externalId,
                        clientId = livenessClientId,
                    ),
                )
                livenessDbRepository.save(
                    SessionCreated(
                        livenessId = livenessId,
                        created = getZonedDateTime(),
                        deviceKeyIdentifier = "deviceKeyIdentifier",
                        userAgent = "userAgent",
                        clientIpAddress = "***********",
                    ),
                )

                livenessDbRepository.save(
                    FaceScanReceived(
                        livenessId = livenessId,
                        created = getZonedDateTime(),
                        faceScanDigest = "faceScanDigest",
                    ),
                )

                livenessDbRepository.save(
                    FaceVerified(
                        livenessId = livenessId,
                        created = getZonedDateTime(),
                        success = true,
                        ageEstimateGroup = AgeEstimateGroup.OVER_THIRTY,
                        platformType = PlatformType.IOS,
                        deviceSDKVersion = "deviceSDKVersion",
                        scanResultBlob = "scanResultBlob",
                        securityCheck = LivenessSecurityCheck(
                            faceScanLivenessCheck = true,
                            auditTrailVerificationCheck = true,
                            sessionTokenCheck = true,
                        ),
                        faceMap = "faceMapContent",
                        auditTrailImage = null,
                    ),
                )
            }

            describe("quando o rosto tem duplicação") {
                it("deve retornar que há conflito e a lista de externalId de conflitos") {
                    livenessDbRepository.save(
                        DuplicationVerified(
                            livenessId = livenessId,
                            created = getZonedDateTime(),
                            duplications = listOf(ExternalId(EXTERNAL_ID)).toSet(),

                        ),
                    )
                    val response = client.enrollmentVerification(externalId)

                    response.status shouldBe HttpStatus.OK

                    response.body()!!.duplications!!.size shouldBe 1
                    response.body()!!.fraudIndications shouldBe null
                }
            }

            describe("quando o rosto está no grupo de fraudadores") {
                it("deve retornar o usuário como possível fraudador") {
                    livenessDbRepository.save(
                        FraudVerified(
                            livenessId = livenessId,
                            created = getZonedDateTime(),
                            userList = setOf(externalId),

                        ),
                    )
                    val response = client.enrollmentVerification(externalId)

                    response.status shouldBe HttpStatus.OK

                    response.body()!!.duplications shouldBe null
                    response.body()!!.fraudIndications!!.size shouldBe 1
                }
            }

            describe("quando o rosto ainda não foi verificado para duplicação") {
                it("deve retornar que não foi verificado") {
                    val response = client.enrollmentVerification(externalId)

                    response.status shouldBe HttpStatus.OK

                    response.body()!!.duplications shouldBe null
                    response.body()!!.fraudIndications shouldBe null
                }
            }

            describe("quando o rosto não teve duplicação no momento do faceScan") {
                it("deve retornar que não houve conflito e uma lista vazia de contas que conflitaram") {
                    livenessDbRepository.save(
                        DuplicationVerified(
                            livenessId = livenessId,
                            created = getZonedDateTime(),
                            duplications = emptySet(),
                        ),
                    )

                    livenessDbRepository.save(
                        FraudVerified(
                            livenessId = livenessId,
                            created = getZonedDateTime(),
                            userList = emptySet(),
                        ),
                    )

                    val response = client.enrollmentVerification(externalId)

                    response.status shouldBe HttpStatus.OK

                    response.body()!!.duplications shouldBe emptyList()
                    response.body()!!.fraudIndications shouldBe emptyList()
                }
            }

            describe("quando não encontrar um evento para o id solicitado") {
                it("deve retornar erro de bad request") {
                    livenessDbRepository.save(
                        DuplicationVerified(
                            livenessId = livenessId,
                            created = getZonedDateTime(),
                            duplications = emptySet(),
                        ),
                    )

                    val exception = org.junit.jupiter.api.assertThrows<HttpClientResponseException> {
                        client.enrollmentVerification(ExternalId("abcde"))
                    }

                    exception.status shouldBe HttpStatus.BAD_REQUEST
                }
            }
        }
    }
}

private suspend fun HttpClient.enrollmentVerification(externalId: ExternalId): HttpResponse<EnrollmentVerificationTO> {
    val password = "senha-correta"
    val username = LivenessClientId("LIVENESS_CLIENT_ID-aaaaaaaa-bbbb-cccc-dddd-eeeeeeeeeeee").value
    fun buildGetVerify(externalId: ExternalId) =
        HttpRequest.GET<EnrollmentVerificationTO>(
            "/internal/enrollmentVerification/${externalId.value}",
        ).basicAuth(username, password)

    return this.exchange(
        buildGetVerify(externalId),
        Argument.of(EnrollmentVerificationTO::class.java),
    ).awaitFirst()
}