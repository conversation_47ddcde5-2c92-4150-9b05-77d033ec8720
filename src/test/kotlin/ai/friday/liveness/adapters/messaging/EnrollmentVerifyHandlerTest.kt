package ai.friday.liveness.adapters.messaging

import ai.friday.liveness.BrazilZonedDateTimeSupplier
import ai.friday.liveness.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.liveness.adapters.dynamo.LivenessEventDynamoDbRepository
import ai.friday.liveness.adapters.parser.getObjectMapper
import ai.friday.liveness.app.integrations.MessageHandlerConfiguration
import ai.friday.liveness.app.liveness.AgeEstimateGroup
import ai.friday.liveness.app.liveness.CompletedLiveness
import ai.friday.liveness.app.liveness.EnrollmentCreated
import ai.friday.liveness.app.liveness.ExternalId
import ai.friday.liveness.app.liveness.FaceMapSearchAdapter
import ai.friday.liveness.app.liveness.FaceMatchGroup
import ai.friday.liveness.app.liveness.FaceScanReceived
import ai.friday.liveness.app.liveness.FaceVerified
import ai.friday.liveness.app.liveness.LivenessId
import ai.friday.liveness.app.liveness.LivenessSecurityCheck
import ai.friday.liveness.app.liveness.MatchCreated
import ai.friday.liveness.app.liveness.PlatformType
import ai.friday.liveness.app.liveness.SessionCreated
import ai.friday.liveness.dynamo.DynamoDBUtils
import ai.friday.liveness.livenessClientIdFridayMain
import arrow.core.right
import io.kotest.core.spec.style.DescribeSpec
import io.kotest.matchers.booleans.shouldBeFalse
import io.kotest.matchers.booleans.shouldBeTrue
import io.kotest.matchers.shouldBe
import io.kotest.matchers.shouldNotBe
import io.kotest.matchers.types.shouldBeInstanceOf
import io.mockk.Called
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import java.util.UUID
import software.amazon.awssdk.services.sqs.model.Message

class EnrollmentVerifyHandlerTest : DescribeSpec() {
    private val setupDynamoDB = DynamoDBUtils.setupDynamoDB()
    private val livenessDbRepository = LivenessEventDynamoDbRepository(setupDynamoDB)

    private val faceMapSearch: FaceMapSearchAdapter = mockk(relaxed = true)

    private val enrollmentVerifyHandler = EnrollmentVerificationHandler(
        sqsClient = mockk(),
        sqsProperties = SQSProperties(
            sqsWaitTime = 0,
            visibilityTimeout = 0,
            maxNumberOfMessages = 0,
            dlqArn = "",
            sqsCoolDownTime = 0,
        ),
        faceMapEnrollmentConfiguration = MessageHandlerConfiguration(
            name = "",
            queueName = "",
            timeWindowCron = null,
            timeWindowToleranceInSeconds = 0,
            consumers = 0,
        ),
        livenessEventRepository = livenessDbRepository,
        faceMapSearch = faceMapSearch,
    )

    private val livenessId = LivenessId.generate()
    private val message = mockk<Message>() {
        every { body() } returns getObjectMapper().writeValueAsString(livenessId)
        every { attributes() } returns emptyMap()
    }

    init {
        beforeTest {
            DynamoDBUtils.cleanDynamoDB()
        }

        describe("ao receber uma mensagem de um liveness invalido") {
            describe("e o liveness não for encontrado") {
                it("nao deve deletar a mensagem sem verificar a duplicação") {
                    val result = enrollmentVerifyHandler.handleMessage(message)
                    result.shouldDeleteMessage.shouldBeFalse()
                    verify {
                        faceMapSearch wasNot Called
                    }
                }
            }
            describe("e o liveness não for do tipo ENROLLMENT") {
                beforeTest {
                    livenessDbRepository.save(
                        MatchCreated(
                            livenessId = livenessId,
                            created = BrazilZonedDateTimeSupplier.getZonedDateTime(),
                            externalId = ExternalId("123"),
                            clientId = livenessClientIdFridayMain,
                        ),
                    )
                }
                it("deve deletar a mensagem sem verificar a duplicação") {
                    val result = enrollmentVerifyHandler.handleMessage(message)
                    result.shouldDeleteMessage.shouldBeTrue()
                    verify {
                        faceMapSearch wasNot Called
                    }
                }
            }

            describe("e o liveness não estiver concluido") {
                beforeTest {
                    livenessDbRepository.save(
                        EnrollmentCreated(
                            livenessId = livenessId,
                            created = BrazilZonedDateTimeSupplier.getZonedDateTime(),
                            externalId = ExternalId("123"),
                            clientId = livenessClientIdFridayMain,
                        ),
                    )
                }
                it("não deve deletar a mensagem sem verificar a duplicação") {
                    val result = enrollmentVerifyHandler.handleMessage(message)
                    result.shouldDeleteMessage.shouldBeFalse()
                    verify {
                        faceMapSearch wasNot Called
                    }
                }
            }
            describe("e o liveness não possuir facemap") {
                beforeTest {
                    livenessDbRepository.save(
                        EnrollmentCreated(
                            livenessId = livenessId,
                            created = getZonedDateTime(),
                            externalId = ExternalId("123"),
                            clientId = livenessClientIdFridayMain,
                        ),
                    )
                    livenessDbRepository.save(
                        SessionCreated(
                            livenessId = livenessId,
                            created = getZonedDateTime(),
                            deviceKeyIdentifier = "deviceKeyIdentifier",
                            userAgent = "userAgent",
                            clientIpAddress = "***********",
                        ),
                    )

                    livenessDbRepository.save(
                        FaceScanReceived(
                            livenessId = livenessId,
                            created = getZonedDateTime(),
                            faceScanDigest = "faceScanDigest",
                        ),
                    )

                    livenessDbRepository.save(
                        FaceVerified(
                            livenessId = livenessId,
                            created = getZonedDateTime(),
                            success = true,
                            ageEstimateGroup = AgeEstimateGroup.OVER_THIRTY,
                            platformType = PlatformType.IOS,
                            deviceSDKVersion = "deviceSDKVersion",
                            scanResultBlob = "scanResultBlob",
                            securityCheck = LivenessSecurityCheck(
                                faceScanLivenessCheck = true,
                                auditTrailVerificationCheck = true,
                                sessionTokenCheck = true,
                            ),
                            faceMap = null,
                            auditTrailImage = null,
                        ),
                    )
                }
                it("não deve deletar a mensagem sem verificar a duplicação") {
                    val result = enrollmentVerifyHandler.handleMessage(message)
                    result.shouldDeleteMessage.shouldBeFalse()
                    verify {
                        faceMapSearch wasNot Called
                    }
                }
            }
        }

        describe("durante a validação de duplicação de rostos") {
            val livenessClientId = livenessClientIdFridayMain
            val enrolledLivenessId = livenessId
            val enrolledExternalId = ExternalId(UUID.randomUUID().toString())

            val otherEnrolledLivenessId = LivenessId.generate()
            val otherEnrolledExternalId = ExternalId(UUID.randomUUID().toString())

            beforeEach {
                livenessDbRepository.save(
                    EnrollmentCreated(
                        livenessId = enrolledLivenessId,
                        created = getZonedDateTime().minusMinutes(4),
                        clientId = livenessClientIdFridayMain,
                        externalId = enrolledExternalId,
                    ),
                )
                livenessDbRepository.save(
                    EnrollmentCreated(
                        livenessId = otherEnrolledLivenessId,
                        created = getZonedDateTime().minusMinutes(4),
                        clientId = livenessClientId,
                        externalId = otherEnrolledExternalId,
                    ),
                )

                listOf(enrolledLivenessId, otherEnrolledLivenessId).forEach {
                    listOf(
                        SessionCreated(
                            livenessId = it,
                            created = getZonedDateTime().minusMinutes(3),
                            deviceKeyIdentifier = "deviceKeyIdentifier",
                            userAgent = "userAgent",
                            clientIpAddress = "***********",
                        ),
                        FaceScanReceived(
                            livenessId = it,
                            created = getZonedDateTime().minusMinutes(2),
                            faceScanDigest = "faceScanDigest",
                        ),
                        FaceVerified(
                            livenessId = it,
                            created = getZonedDateTime().minusMinutes(1),
                            success = true,
                            ageEstimateGroup = AgeEstimateGroup.OVER_THIRTY,
                            platformType = PlatformType.IOS,
                            deviceSDKVersion = "deviceSDKVersion",
                            scanResultBlob = "scanResultBlob",
                            securityCheck = LivenessSecurityCheck(
                                faceScanLivenessCheck = true,
                                auditTrailVerificationCheck = true,
                                sessionTokenCheck = true,
                            ),
                            faceMap = "faceMapContent",
                            auditTrailImage = null,
                        ),
                    ).forEach {
                        livenessDbRepository.save(it)
                    }
                }
            }

            describe("e for um rosto que nunca fez enroll antes") {
                beforeEach {
                    coEvery {
                        faceMapSearch.search3D(any(), any(), any())
                    } returns emptyList<ExternalId>().right()
                    coEvery {
                        faceMapSearch.search3D(any(), group = FaceMatchGroup.FRAUD_USERS, clientId = any())
                    } returns emptyList<ExternalId>().right()
                }

                it("deve cadastrar o rosto, gerar evento duplicado e apagar a mensagem da fila") {
                    val response = enrollmentVerifyHandler.handleMessage(message)

                    response.shouldDeleteMessage.shouldBeTrue()

                    coVerify {
                        faceMapSearch.saveFaceMap(enrolledExternalId, any(), any(), any())
                    }

                    with(livenessDbRepository.findEnrollmentByExternalId(enrolledExternalId)) {
                        shouldBeInstanceOf<CompletedLiveness>()
                        duplications shouldBe emptyList()
                    }
                }
            }

            describe("e for um rosto que já fez enroll antes") {
                beforeEach {
                    coEvery {
                        faceMapSearch.search3D(any(), any(), any())
                    } returns listOf(otherEnrolledExternalId).right()
                    coEvery {
                        faceMapSearch.search3D(any(), group = FaceMatchGroup.FRAUD_USERS, clientId = any())
                    } returns emptyList<ExternalId>().right()
                }

                it("deve gerar evento de duplicaçao para cada duplicaçao encontrada e apagar a mensagem") {
                    val response = enrollmentVerifyHandler.handleMessage(message)

                    response.shouldDeleteMessage.shouldBeTrue()

                    with(livenessDbRepository.findEnrollmentByExternalId(otherEnrolledExternalId)) {
                        shouldBeInstanceOf<CompletedLiveness>()
                        duplications shouldNotBe null
                        duplications?.size shouldBe 1
                        duplications?.first() shouldBe enrolledExternalId
                    }

                    with(livenessDbRepository.findEnrollmentByExternalId(enrolledExternalId)) {
                        shouldBeInstanceOf<CompletedLiveness>()
                        duplications?.size shouldBe 1
                        duplications?.first() shouldBe otherEnrolledExternalId
                    }
                }

                describe("e o rosto estiver no grupo de fraudadores") {
                    beforeEach {
                        coEvery {
                            faceMapSearch.search3D(any(), group = FaceMatchGroup.FRAUD_USERS, clientId = any())
                        } returns listOf(enrolledExternalId).right()
                    }

                    it("deve gerar evento de fraude e apagar a mensagem") {
                        val response = enrollmentVerifyHandler.handleMessage(message)

                        response.shouldDeleteMessage.shouldBeTrue()

                        with(livenessDbRepository.findEnrollmentByExternalId(enrolledExternalId)) {
                            shouldBeInstanceOf<CompletedLiveness>()
                            fraudIndications?.size shouldBe 1
                            fraudIndications?.first() shouldBe enrolledExternalId
                        }
                    }
                }
            }
        }
    }
}