package ai.friday.liveness.adapters

import ai.friday.liveness.app.auth.AuthConfig
import io.kotest.core.spec.style.DescribeSpec
import io.kotest.matchers.shouldBe
import io.micronaut.test.extensions.kotest.annotation.MicronautTest

@MicronautTest
class AuthConfigTest(private val configuration: AuthConfig) : DescribeSpec() {
    init {
        describe("quando houver 3 tenants configurados") {
            it("deve carregar ambos") {
                configuration.tenants.size shouldBe 3
                configuration.tenants.values.filter { it.allowReplay }.size shouldBe 1
            }
        }
    }
}