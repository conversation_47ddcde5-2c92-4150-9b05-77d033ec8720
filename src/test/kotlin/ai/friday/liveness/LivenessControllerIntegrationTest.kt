package ai.friday.liveness

import ai.friday.liveness.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.liveness.adapters.api.CheckRequestTO
import ai.friday.liveness.adapters.dynamo.LivenessEventDynamoDbRepository
import ai.friday.liveness.adapters.micronaut.FeaturesConfigurationMicronaut
import ai.friday.liveness.app.integrations.MessagePublisher
import ai.friday.liveness.app.liveness.AgeEstimateGroup
import ai.friday.liveness.app.liveness.CompletedLiveness
import ai.friday.liveness.app.liveness.EnrollmentCreated
import ai.friday.liveness.app.liveness.ExternalId
import ai.friday.liveness.app.liveness.FaceMapSearchAdapter
import ai.friday.liveness.app.liveness.FaceVerified
import ai.friday.liveness.app.liveness.FeatureConfiguration
import ai.friday.liveness.app.liveness.LivenessAdapter
import ai.friday.liveness.app.liveness.LivenessCheckResult
import ai.friday.liveness.app.liveness.LivenessEvent
import ai.friday.liveness.app.liveness.LivenessEventRepository
import ai.friday.liveness.app.liveness.LivenessId
import ai.friday.liveness.app.liveness.LivenessSecurityCheck
import ai.friday.liveness.app.liveness.LivenessStatus
import ai.friday.liveness.app.liveness.MatchCreated
import ai.friday.liveness.app.liveness.PendingFaceVerification
import ai.friday.liveness.app.liveness.PendingSessionCreation
import ai.friday.liveness.app.liveness.PlatformType
import ai.friday.liveness.app.liveness.SessionCreated
import ai.friday.liveness.app.session.SessionAdapter
import ai.friday.liveness.app.session.SessionTokenResult
import ai.friday.liveness.dynamo.DynamoDBUtils
import arrow.core.right
import io.kotest.core.spec.style.DescribeSpec
import io.kotest.matchers.shouldBe
import io.kotest.matchers.types.shouldBeInstanceOf
import io.micronaut.http.HttpRequest
import io.micronaut.http.HttpResponse
import io.micronaut.http.HttpStatus
import io.micronaut.http.client.HttpClient
import io.micronaut.http.client.exceptions.HttpClientResponseException
import io.micronaut.runtime.server.EmbeddedServer
import io.micronaut.test.annotation.MockBean
import io.mockk.coEvery
import io.mockk.just
import io.mockk.mockk
import io.mockk.runs
import java.util.UUID
import kotlinx.coroutines.reactive.awaitFirst
import org.junit.jupiter.api.assertThrows

internal abstract class LivenessControllerIntegrationTest(
    embeddedServer: EmbeddedServer,
    val firstEvent: LivenessEvent,
) : DescribeSpec() {
    internal val livenessDbRepository: LivenessEventRepository =
        embeddedServer.applicationContext.createBean(LivenessEventRepository::class.java)
    internal val client: HttpClient =
        embeddedServer.applicationContext.createBean(HttpClient::class.java, embeddedServer.url)

    private val setupDynamoDB = DynamoDBUtils.setupDynamoDB()
    private val livenessEventDbRepository = LivenessEventDynamoDbRepository(setupDynamoDB)
    internal val livenessAdapterMock: LivenessAdapter = mockk { }
    internal val sessionAdapterMock: SessionAdapter = mockk { }

    internal val faceMapSearchAdapterMock: FaceMapSearchAdapter = mockk {
        coEvery { search3D(any(), any(), any()) } returns emptyList<ExternalId>().right()
        coEvery { saveFaceMap(any(), any(), any(), any()) } just runs
    }

    @MockBean(SessionAdapter::class)
    fun getSessionAdapter(): SessionAdapter = sessionAdapterMock

    @MockBean(LivenessAdapter::class)
    fun getLivenessAdapter(): LivenessAdapter = livenessAdapterMock

    @MockBean(FeaturesConfigurationMicronaut::class)
    fun getFeaturesConfigurationMicronaut(): FeatureConfiguration = featureConfiguration
    private val featureConfiguration = mockk<FeatureConfiguration>() {
        coEvery { oneToManySearch } returns true
    }

    @MockBean(FaceMapSearchAdapter::class)
    fun getFaceMapSearchAdapter(): FaceMapSearchAdapter = faceMapSearchAdapterMock

    @MockBean(LivenessEventDynamoDbRepository::class)
    fun getLivenessEventRepository(): LivenessEventRepository = livenessEventDbRepository

    @MockBean(MessagePublisher::class)
    fun getMessagePublisher(): MessagePublisher = messagePublisher
    private val messagePublisher: MessagePublisher = mockk(relaxUnitFun = true)

    val livenessId = firstEvent.livenessId
    val livenessClientId = when (firstEvent) {
        is EnrollmentCreated -> firstEvent.clientId
        is MatchCreated -> firstEvent.clientId
        else -> TODO("Tipo não conhecido para iniciar Liveness")
    }

    private val defaultFaceMapBase64 = "RkFDRV9NQVBfRkFLRQ=="

    abstract suspend fun prepareDatabaseToMakeInitialValidationPass(faceMap: String? = defaultFaceMapBase64)

    init {
        beforeEach {
            DynamoDBUtils.cleanDynamoDB()

            coEvery { sessionAdapterMock.createSession(any()) } returns SessionTokenResult(token = "session-token").right()
            coEvery { livenessAdapterMock.checkLiveness(any()) } returns LivenessCheckResult.createSuccessResult(
                ageEstimateGroup = AgeEstimateGroup.OVER_THIRTY,
                platformType = PlatformType.IOS,
                deviceSDKVersion = "deviceSDKVersion",
                securityCheck = LivenessSecurityCheck(
                    sessionTokenCheck = true,
                    auditTrailVerificationCheck = true,
                    faceScanLivenessCheck = true,
                ),
                faceMap = ByteWrapper(defaultFaceMapBase64),
                scanResultBlobFactory = { "blob" },
            ).right()
        }

        describe("quando o liveness id usado não existe") {
            val unexistentLivenessId = LivenessId.generate()

            describe("quando for criar uma sessão") {
                it("deve retornar unauthorized") {
                    val exception = assertThrows<HttpClientResponseException> {
                        client.createSession(unexistentLivenessId)
                    }

                    exception.status shouldBe HttpStatus.UNAUTHORIZED
                }
            }

            describe("quando for realizar uma checagem") {
                it("deve retornar unauthorized") {
                    val exception = assertThrows<HttpClientResponseException> {
                        client.checkLiveness(unexistentLivenessId)
                    }

                    exception.status shouldBe HttpStatus.UNAUTHORIZED
                }
            }
        }

        describe("quando o liveness id usado existe") {
            beforeEach {
                livenessDbRepository.save(
                    firstEvent,
                )
            }

            describe("quando for criar uma sessão") {
                it("deve retornar ok") {
                    val response = client.createSession(livenessId)

                    response.status shouldBe HttpStatus.OK
                }

                it("deve ficar pendente de verificação facial") {
                    client.createSession(livenessId)

                    with(livenessDbRepository.findByIdOrNull(livenessId)) {
                        shouldBeInstanceOf<PendingFaceVerification>()
                        status shouldBe LivenessStatus.CREATED
                        attempt shouldBe 0
                    }
                }
            }

            describe("quando for realizar uma checagem") {
                describe("e não tiver criado a sessão") {
                    it("deve retornar unauthorized") {
                        val exception = assertThrows<HttpClientResponseException> {
                            client.checkLiveness(livenessId)
                        }

                        exception.status shouldBe HttpStatus.UNAUTHORIZED
                    }

                    it("deve continuar como pendente de criação de sessão") {
                        assertThrows<HttpClientResponseException> {
                            client.checkLiveness(livenessId)
                        }

                        with(livenessDbRepository.findByIdOrNull(livenessId)) {
                            shouldBeInstanceOf<PendingSessionCreation>()
                        }
                    }
                }

                describe("e já tiver criado a sessão") {
                    beforeEach {
                        client.createSession(livenessId)
                    }

                    describe("e conseguir extrair um facemap") {
                        val faceMap = ByteWrapper(defaultFaceMapBase64)

                        describe("e a checagem passar") {
                            beforeEach {
                                prepareLivenessAdapterMock(
                                    LivenessCheckResult.createSuccessResult(
                                        ageEstimateGroup = AgeEstimateGroup.OVER_THIRTY,
                                        platformType = PlatformType.IOS,
                                        deviceSDKVersion = "deviceSDKVersion",
                                        securityCheck = LivenessSecurityCheck(
                                            sessionTokenCheck = true,
                                            auditTrailVerificationCheck = true,
                                            faceScanLivenessCheck = true,
                                        ),
                                        faceMap = faceMap,
                                        scanResultBlobFactory = { "blob" },
                                    ),
                                )
                                prepareDatabaseToMakeInitialValidationPass()
                            }

                            it("deve retornar ok") {
                                with(client.checkLiveness(livenessId)) {
                                    status shouldBe HttpStatus.OK
                                }
                            }

                            it("deve mudar o estado do enrollment para completo") {
                                client.checkLiveness(livenessId)

                                with(livenessDbRepository.findByIdOrNull(livenessId)) {
                                    shouldBeInstanceOf<CompletedLiveness>()
                                    status shouldBe LivenessStatus.PROCESSED
                                }
                            }
                        }

                        describe("e checagem não passar") {
                            beforeEach {
                                prepareLivenessAdapterMock(
                                    LivenessCheckResult.createSuccessResult(
                                        ageEstimateGroup = AgeEstimateGroup.OVER_THIRTY,
                                        platformType = PlatformType.IOS,
                                        deviceSDKVersion = "deviceSDKVersion",
                                        securityCheck = LivenessSecurityCheck(
                                            sessionTokenCheck = true,
                                            auditTrailVerificationCheck = true,
                                            faceScanLivenessCheck = false,
                                        ),
                                        faceMap = faceMap,
                                        scanResultBlobFactory = { "blob" },
                                    ),
                                )
                                prepareDatabaseToMakeInitialValidationPass()
                            }

                            it("deve retornar ok") {
                                with(client.checkLiveness(livenessId)) {
                                    status shouldBe HttpStatus.OK
                                }
                            }

                            it("deve manter o enrollment como pendente de verificação") {
                                client.checkLiveness(livenessId)

                                with(livenessDbRepository.findByIdOrNull(livenessId)) {
                                    shouldBeInstanceOf<PendingFaceVerification>()
                                    status shouldBe LivenessStatus.CREATED
                                }
                            }
                        }
                    }

                    describe("e não conseguir extrair um facemap") {
                        beforeEach {
                            prepareLivenessAdapterMock(
                                LivenessCheckResult.createFailureResult(
                                    ageEstimateGroup = AgeEstimateGroup.OVER_THIRTY,
                                    platformType = PlatformType.IOS,
                                    deviceSDKVersion = "deviceSDKVersion",
                                    securityCheck = LivenessSecurityCheck(
                                        sessionTokenCheck = true,
                                        auditTrailVerificationCheck = true,
                                        faceScanLivenessCheck = true,
                                    ),
                                    scanResultBlobFactory = { "blob" },
                                ),
                            )
                            prepareDatabaseToMakeInitialValidationPass()
                        }

                        it("deve retornar ok") {
                            with(client.checkLiveness(livenessId)) {
                                status shouldBe HttpStatus.OK
                            }
                        }

                        it("deve manter o enrollment como pendente de verificação") {
                            client.checkLiveness(livenessId)

                            with(livenessDbRepository.findByIdOrNull(livenessId)) {
                                shouldBeInstanceOf<PendingFaceVerification>()
                                status shouldBe LivenessStatus.CREATED
                                attempt shouldBe 1
                            }
                        }
                    }
                }

                describe("e detectar um session replay") {
                    beforeEach {
                        val otherLivenessId = LivenessId.generate()

                        livenessDbRepository.save(
                            EnrollmentCreated(
                                livenessId = otherLivenessId,
                                created = getZonedDateTime(),
                                clientId = livenessClientId,
                                externalId = ExternalId(UUID.randomUUID().toString()),
                            ),
                        )

                        client.createSession(otherLivenessId)
                        client.checkLiveness(otherLivenessId)

                        client.createSession(livenessId)
                    }

                    it("deve retornar unauthorized") {
                        with(
                            assertThrows<HttpClientResponseException> {
                                client.checkLiveness(livenessId)
                            },
                        ) {
                            status shouldBe HttpStatus.UNAUTHORIZED
                        }
                    }

                    it("deve continuar como pendente de validação de face") { // TODO - não deveria ter concluido com falha?
                        assertThrows<HttpClientResponseException> {
                            client.checkLiveness(livenessId)
                        }

                        with(livenessDbRepository.findByIdOrNull(livenessId)) {
                            shouldBeInstanceOf<PendingFaceVerification>()
                            status shouldBe LivenessStatus.CREATED
                        }
                    }
                }
            }
        }

        describe("ao usar um liveness id de enrollment já completo") {
            beforeEach {
                livenessDbRepository.save(
                    firstEvent,
                )
                livenessDbRepository.save(
                    SessionCreated(
                        livenessId = livenessId,
                        created = getZonedDateTime(),
                        deviceKeyIdentifier = "",
                        userAgent = "",
                        clientIpAddress = "",
                    ),
                )
                livenessDbRepository.save(
                    FaceVerified(
                        livenessId = livenessId,
                        created = getZonedDateTime(),
                        success = true,
                        ageEstimateGroup = AgeEstimateGroup.OVER_THIRTY,
                        platformType = PlatformType.IOS,
                        deviceSDKVersion = "",
                        securityCheck = LivenessSecurityCheck(
                            sessionTokenCheck = true,
                            auditTrailVerificationCheck = true,
                            faceScanLivenessCheck = true,
                        ),
                        faceMap = defaultFaceMapBase64,
                        auditTrailImage = "QVVESVRfVFJBSUxfSU1BR0VfRkFLRQ==",
                        scanResultBlob = "",
                    ),
                )
            }

            describe("quando for criar uma sessão") {
                it("deve retornar bad request") {
                    val exception = assertThrows<HttpClientResponseException> {
                        client.createSession(livenessId)
                    }
                    exception.status shouldBe HttpStatus.UNAUTHORIZED
                }
            }

            describe("quando for realizar uma checagem") {

                val exception = assertThrows<HttpClientResponseException> {
                    client.checkLiveness(livenessId)
                }

                it("deve retornar unauthorized") {
                    exception.status shouldBe HttpStatus.UNAUTHORIZED
                }
            }
        }
    }

    abstract fun prepareLivenessAdapterMock(result: LivenessCheckResult)
}

internal suspend fun HttpClient.checkLiveness(livenessId: LivenessId): HttpResponse<*> {
    fun buildPostCheckLiveness(livenessId: LivenessId) =
        HttpRequest.POST(
            "/check",
            CheckRequestTO(
                faceScan = "base64",
                auditTrailImage = "base64",
                lowQualityAuditTrailImage = "base64",
                livenessId = livenessId.value,
            ),
        )
            .headers(mapOf("X-Device-Key" to "key", "X-User-Agent" to "agent"))
    return this.exchange(buildPostCheckLiveness(livenessId)).awaitFirst()
}

internal suspend fun HttpClient.createSession(livenessId: LivenessId): HttpResponse<*> {
    fun buildPostLivenessSession(livenessId: LivenessId) =
        HttpRequest.POST("/session", """{"operationId":"${livenessId.value}","type":"LivenessCheck"}""")
            .headers(
                mapOf(
                    "X-API-VERSION" to "2",
                    "X-Device-Key" to "key",
                    "X-User-Agent" to "agent",
                ),
            )

    return this.exchange(buildPostLivenessSession(livenessId)).awaitFirst()
}