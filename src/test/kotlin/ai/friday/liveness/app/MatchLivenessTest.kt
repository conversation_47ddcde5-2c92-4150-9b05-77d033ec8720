package ai.friday.liveness.app

import ai.friday.liveness.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.liveness.app.liveness.ExternalId
import ai.friday.liveness.app.liveness.LivenessId
import ai.friday.liveness.app.liveness.MatchCreated
import ai.friday.liveness.livenessClientIdFridayMain

class MatchLivenessTest : LivenessTest(
    MatchCreated(
        livenessId = LivenessId.generate(),
        created = getZonedDateTime(),
        clientId = livenessClientIdFridayMain,
        externalId = ExternalId("ACCOUNT-1"),
    ),
)