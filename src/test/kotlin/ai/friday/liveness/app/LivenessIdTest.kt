package ai.friday.liveness.app

import ai.friday.liveness.app.liveness.LivenessId
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.core.spec.style.AnnotationSpec
import java.util.UUID

class LivenessIdTest : AnnotationSpec() {
    @Test
    fun `Não deve aceitar prefixos diferentes`() {
        shouldThrow<IllegalArgumentException> {
            LivenessId("LIVE-" + UUID.randomUUID().toString())
        }
    }

    @Test
    fun `não deve aceitar um sufixo diferente de um uuid`() {
        shouldThrow<IllegalArgumentException> {
            LivenessId("LIVENESS_ID-teste")
        }
    }
}