package ai.friday.liveness.app.liveness

import ai.friday.liveness.app.auth.AuthConfig
import io.kotest.core.spec.style.DescribeSpec
import io.kotest.matchers.shouldBe

/**
 * Teste unitário simples para verificar se a configuração de provider está funcionando corretamente.
 * Este teste não depende de infraestrutura externa.
 */
internal class ProviderConfigurationTest : DescribeSpec({

    describe("Provider configuration") {

        it("should create correct enum values") {
            LivenessProvider.FACETECH.value shouldBe "facetech"
        }

        it("should find provider by value") {
            val provider = LivenessProvider.values().find { it.value == "facetech" }
            provider shouldBe LivenessProvider.FACETECH
        }

        it("should fallback to FACETECH for unknown value") {
            val provider = LivenessProvider.values().find { it.value == "unknown" } ?: LivenessProvider.FACETECH
            provider shouldBe LivenessProvider.FACETECH
        }

        it("should create tenant config with default provider") {
            val tenantConfig = AuthConfig.TenantConfig()
            tenantConfig.provider shouldBe LivenessProvider.FACETECH.value
        }
    }
})