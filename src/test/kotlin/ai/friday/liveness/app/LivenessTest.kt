package ai.friday.liveness.app

import ai.friday.liveness.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.liveness.app.liveness.AgeEstimateGroup
import ai.friday.liveness.app.liveness.CompletedLiveness
import ai.friday.liveness.app.liveness.ExternalId
import ai.friday.liveness.app.liveness.FaceVerified
import ai.friday.liveness.app.liveness.Liveness
import ai.friday.liveness.app.liveness.LivenessEvent
import ai.friday.liveness.app.liveness.LivenessEventType
import ai.friday.liveness.app.liveness.LivenessId
import ai.friday.liveness.app.liveness.LivenessSecurityCheck
import ai.friday.liveness.app.liveness.LivenessType
import ai.friday.liveness.app.liveness.PendingFaceVerification
import ai.friday.liveness.app.liveness.PendingSessionCreation
import ai.friday.liveness.app.liveness.PlatformType
import ai.friday.liveness.app.liveness.SessionCreated
import ai.friday.liveness.app.liveness.VerificationResult
import io.kotest.core.spec.style.DescribeSpec
import io.kotest.data.Row2
import io.kotest.data.forAll
import io.kotest.matchers.shouldBe
import io.kotest.matchers.types.shouldBeInstanceOf
import org.junit.jupiter.api.assertDoesNotThrow
import org.junit.jupiter.api.assertThrows

abstract class LivenessTest(firstEvent: LivenessEvent) : DescribeSpec() {

    private val sessionCreated = SessionCreated(
        livenessId = firstEvent.livenessId,
        created = getZonedDateTime().plusMinutes(1),
        deviceKeyIdentifier = "deviceKeyIdentifier",
        userAgent = "userAgent",
        clientIpAddress = "***********",
    )

    private val successFaceVerified = FaceVerified(
        livenessId = firstEvent.livenessId,
        created = getZonedDateTime().plusMinutes(2),
        success = true,
        ageEstimateGroup = AgeEstimateGroup.OVER_EIGHTEEN,
        platformType = PlatformType.ANDROID,
        deviceSDKVersion = "deviceSDKVersion",
        securityCheck = LivenessSecurityCheck(
            sessionTokenCheck = true,
            auditTrailVerificationCheck = true,
            faceScanLivenessCheck = true,
        ),
        faceMap = "RkFDRV9NQVBfRkFLRQ==",
        auditTrailImage = "QVVESVRfVFJBSUxfSU1BR0VfRkFLRQ==",
        scanResultBlob = "foo",
    )

    private val failedFaceVerifiedWithFaceMap = FaceVerified(
        livenessId = firstEvent.livenessId,
        created = getZonedDateTime().plusMinutes(3),
        success = false,
        ageEstimateGroup = AgeEstimateGroup.OVER_EIGHTEEN,
        platformType = PlatformType.ANDROID,
        deviceSDKVersion = "deviceSDKVersion",
        securityCheck = LivenessSecurityCheck(
            sessionTokenCheck = true,
            auditTrailVerificationCheck = true,
            faceScanLivenessCheck = false,
        ),
        faceMap = "RkFDRV9NQVBfRkFLRQ==",
        auditTrailImage = "QVVESVRfVFJBSUxfSU1BR0VfRkFLRQ==",
        scanResultBlob = "foo",
    )

    private val expectedLivenessType = when (firstEvent.eventType) {
        LivenessEventType.ENROLLMENT_CREATED -> LivenessType.ENROLLMENT
        LivenessEventType.MATCH_CREATED -> LivenessType.MATCH
        else -> TODO("Tipo não conhecido para iniciar liveness")
    }

    private val failedFaceVerifiedAgainWithFaceMap = failedFaceVerifiedWithFaceMap.copy(
        created = getZonedDateTime().plusMinutes(4),
    )

    private val failedFaceVerifiedWithoutFaceMap = FaceVerified(
        livenessId = firstEvent.livenessId,
        created = getZonedDateTime().plusMinutes(5),
        success = false,
        ageEstimateGroup = AgeEstimateGroup.OVER_EIGHTEEN,
        platformType = PlatformType.ANDROID,
        deviceSDKVersion = "deviceSDKVersion",
        securityCheck = LivenessSecurityCheck(
            sessionTokenCheck = false,
            auditTrailVerificationCheck = false,
            faceScanLivenessCheck = false,
        ),
        faceMap = null,
        auditTrailImage = null,
        scanResultBlob = "foo",
    )

    init {
        describe("ao tentar criar um liveness sem histórico") {
            it("deve falhar") {
                assertThrows<IllegalStateException> {
                    Liveness.build()
                }
            }
        }

        describe("ao tentar criar um liveness onde o primeiro evento não é o de criação") {
            it("deve falhar") {
                assertThrows<IllegalStateException> {
                    Liveness.build(
                        SessionCreated(
                            livenessId = LivenessId.generate(),
                            created = getZonedDateTime(),
                            deviceKeyIdentifier = "device-key-id",
                            userAgent = "ua-string",
                            clientIpAddress = "***************",
                        ),
                    )
                }
            }
        }

        describe("ao tentar criar um liveness") {
            describe("e só tem o evento de criação") {
                it("deve criar o liveness com sucesso") {
                    assertDoesNotThrow {
                        Liveness.build(
                            firstEvent,
                        )
                    }
                }

                it("deve ficar aguardando a criação de sessão") {
                    val liveness = Liveness.build(
                        firstEvent,
                    )

                    liveness shouldBe PendingSessionCreation(
                        id = firstEvent.livenessId,
                        externalId = ExternalId("ACCOUNT-1"),
                        type = expectedLivenessType,
                        createdAt = firstEvent.created,
                        updatedAt = firstEvent.created,
                        clientId = liveness.clientId,
                    )
                }
            }
        }

        describe("quando o liveness está aguardando a criação da sessão") {
            describe("e pede para criar a sessão") {
                it("deve ficar aguardando a verificação da face") {
                    val liveness = Liveness.build(
                        firstEvent,
                        sessionCreated,
                    )

                    liveness shouldBe PendingFaceVerification(
                        id = firstEvent.livenessId,
                        externalId = ExternalId("ACCOUNT-1"),
                        type = expectedLivenessType,
                        createdAt = firstEvent.created,
                        updatedAt = sessionCreated.created,
                        deviceKeyIdentifier = sessionCreated.deviceKeyIdentifier,
                        userAgent = sessionCreated.userAgent,
                        clientIpAddress = sessionCreated.clientIpAddress,
                        attempt = 0,
                        clientId = liveness.clientId,
                    )
                }

                describe("e pediu para criar a sessão novamente") {
                    it("deve continuar aguardando a verificação da face") {
                        val otherSessionCreated = sessionCreated.copy(
                            created = getZonedDateTime(),
                            deviceKeyIdentifier = "other-deviceKeyIdentifier",
                            userAgent = "other-userAgent",
                            clientIpAddress = "***********",
                        )

                        val liveness = Liveness.build(
                            firstEvent,
                            sessionCreated,
                            otherSessionCreated,
                        )

                        liveness.shouldBeInstanceOf<PendingFaceVerification>()
                    }
                }
            }
        }

        describe("quando o liveness está aguardando a verificação da face") {
            describe("e pede para criar sessão novamente") {

                it("deve continuar aguardando a verificação da face com os novos dados recebidos") {
                    val otherSessionCreated = sessionCreated.copy(
                        created = getZonedDateTime(),
                        deviceKeyIdentifier = "other-deviceKeyIdentifier",
                        userAgent = "other-userAgent",
                        clientIpAddress = "***********",
                    )

                    val liveness = Liveness.build(
                        firstEvent,
                        sessionCreated,
                        otherSessionCreated,
                    )

                    liveness shouldBe PendingFaceVerification(
                        id = firstEvent.livenessId,
                        externalId = ExternalId("ACCOUNT-1"),
                        type = expectedLivenessType,
                        createdAt = firstEvent.created,
                        updatedAt = otherSessionCreated.created,
                        deviceKeyIdentifier = otherSessionCreated.deviceKeyIdentifier,
                        userAgent = otherSessionCreated.userAgent,
                        clientIpAddress = otherSessionCreated.clientIpAddress,
                        attempt = 0,
                        clientId = liveness.clientId,
                    )
                }
            }

            describe("e pede para processar uma verificação de face") {
                describe("se a face foi verificada com sucesso") {
                    it("deve completar com sucesso o liveness com os dados recebido") {
                        val liveness = Liveness.build(
                            firstEvent,
                            sessionCreated,
                            successFaceVerified,
                        )

                        with(liveness as CompletedLiveness) {
                            id shouldBe firstEvent.livenessId
                            externalId shouldBe ExternalId("ACCOUNT-1")
                            type shouldBe expectedLivenessType
                            createdAt shouldBe firstEvent.created
                            updatedAt shouldBe successFaceVerified.created
                            ageEstimateGroup shouldBe successFaceVerified.ageEstimateGroup
                            platformType shouldBe successFaceVerified.platformType
                            deviceSDKVersion shouldBe successFaceVerified.deviceSDKVersion
                            result shouldBe VerificationResult.Success
                            duplications shouldBe null
                            fraudIndications shouldBe null
                        }
                    }
                }

                describe("se não conseguiu sequer capturar uma face") {
                    it("deve permanecer aguardando por uma nova verificação da face") {
                        val liveness = Liveness.build(
                            firstEvent,
                            sessionCreated,
                            failedFaceVerifiedWithoutFaceMap,
                        )

                        liveness shouldBe PendingFaceVerification(
                            id = firstEvent.livenessId,
                            externalId = ExternalId("ACCOUNT-1"),
                            type = expectedLivenessType,
                            createdAt = firstEvent.created,
                            updatedAt = failedFaceVerifiedWithoutFaceMap.created,
                            deviceKeyIdentifier = sessionCreated.deviceKeyIdentifier,
                            userAgent = sessionCreated.userAgent,
                            clientIpAddress = sessionCreated.clientIpAddress,
                            attempt = 1,
                            clientId = liveness.clientId,
                        )
                    }
                }

                describe("se a face foi capturada, mas não foi verificada com sucesso") {
                    it("deve permanecer aguardando por uma nova verificação da face") {
                        val liveness = Liveness.build(
                            firstEvent,
                            sessionCreated,
                            failedFaceVerifiedWithFaceMap,
                        )

                        liveness shouldBe PendingFaceVerification(
                            id = firstEvent.livenessId,
                            externalId = ExternalId("ACCOUNT-1"),
                            type = expectedLivenessType,
                            createdAt = firstEvent.created,
                            updatedAt = failedFaceVerifiedWithFaceMap.created,
                            deviceKeyIdentifier = sessionCreated.deviceKeyIdentifier,
                            userAgent = sessionCreated.userAgent,
                            clientIpAddress = sessionCreated.clientIpAddress,
                            attempt = 1,
                            clientId = liveness.clientId,
                        )
                    }

                    describe("e falhar novamente para verificar a face") {
                        it("deve aumentar o contador de tentativas e permanecer aguardando por uma nova verificação da face") {
                            val liveness = Liveness.build(
                                firstEvent,
                                sessionCreated,
                                failedFaceVerifiedWithFaceMap,
                                failedFaceVerifiedAgainWithFaceMap,
                            )

                            liveness shouldBe PendingFaceVerification(
                                id = firstEvent.livenessId,
                                externalId = ExternalId("ACCOUNT-1"),
                                type = expectedLivenessType,
                                createdAt = firstEvent.created,
                                updatedAt = failedFaceVerifiedAgainWithFaceMap.created,
                                deviceKeyIdentifier = sessionCreated.deviceKeyIdentifier,
                                userAgent = sessionCreated.userAgent,
                                clientIpAddress = sessionCreated.clientIpAddress,
                                attempt = 2,
                                clientId = liveness.clientId,
                            )
                        }
                    }
                }
            }

            describe("e para quaisquer outros eventos") {

                forAll(
                    Row2("enrollmentCreated", firstEvent),
                    Row2("sessionCreated", sessionCreated),
                    Row2("successFaceVerified", successFaceVerified),
                    Row2("failedFaceVerifiedWithFaceMap", failedFaceVerifiedWithFaceMap),
                    Row2("failedFaceVerifiedWithoutFaceMap", failedFaceVerifiedWithoutFaceMap),
                ) { name, event ->

                    it("deve falhar para o evento $name") {
                        val liveness = Liveness.build(
                            firstEvent,
                            sessionCreated,
                            successFaceVerified,
                        )

                        assertThrows<IllegalStateException> {
                            liveness.apply(event)
                        }
                    }
                }
            }
        }
    }
}