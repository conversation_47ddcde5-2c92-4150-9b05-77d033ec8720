package ai.friday.liveness.dynamo

import ai.friday.liveness.adapters.dynamo.GlobalSecondaryIndexes
import ai.friday.liveness.adapters.dynamo.INDEX_1_PARTITION_KEY
import ai.friday.liveness.adapters.dynamo.INDEX_1_SCAN_KEY
import ai.friday.liveness.adapters.dynamo.LIVENESS_TABLE_NAME
import ai.friday.liveness.adapters.dynamo.PARTITION_KEY
import ai.friday.liveness.adapters.dynamo.SCAN_KEY
import java.net.URI
import software.amazon.awssdk.auth.credentials.AwsBasicCredentials
import software.amazon.awssdk.auth.credentials.StaticCredentialsProvider
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedClient
import software.amazon.awssdk.regions.Region
import software.amazon.awssdk.services.dynamodb.DynamoDbClient
import software.amazon.awssdk.services.dynamodb.model.AttributeDefinition
import software.amazon.awssdk.services.dynamodb.model.CreateTableRequest
import software.amazon.awssdk.services.dynamodb.model.CreateTableResponse
import software.amazon.awssdk.services.dynamodb.model.DeleteTableRequest
import software.amazon.awssdk.services.dynamodb.model.DescribeTableRequest
import software.amazon.awssdk.services.dynamodb.model.GlobalSecondaryIndex
import software.amazon.awssdk.services.dynamodb.model.KeySchemaElement
import software.amazon.awssdk.services.dynamodb.model.KeyType
import software.amazon.awssdk.services.dynamodb.model.Projection
import software.amazon.awssdk.services.dynamodb.model.ProjectionType
import software.amazon.awssdk.services.dynamodb.model.ProvisionedThroughput
import software.amazon.awssdk.services.dynamodb.model.ResourceNotFoundException
import software.amazon.awssdk.services.dynamodb.model.ScalarAttributeType
import software.amazon.awssdk.services.dynamodb.model.TableStatus

object DynamoDBUtils {

    private val dynamoDbClient: DynamoDbClient by lazy {
        DynamoDbClient
            .builder()
            .endpointOverride(URI.create("http://localhost:8000"))
            .credentialsProvider(StaticCredentialsProvider.create(AwsBasicCredentials.create("abc", "qwe")))
            .region(Region.US_EAST_1)
            .build()
    }

    private val dynamoDbEnhancedClient: DynamoDbEnhancedClient by lazy {
        DynamoDbEnhancedClient
            .builder()
            .dynamoDbClient(dynamoDbClient).build()
    }

    fun setupDynamoDB(): DynamoDbEnhancedClient {
        LocalDbCreationRule.startServer()
        createEventTable()
        return dynamoDbEnhancedClient
    }

    fun cleanDynamoDB() {
        val request = DeleteTableRequest.builder().tableName(LIVENESS_TABLE_NAME).build()
        dynamoDbClient.deleteTable(request)
        createEventTable()
    }

    private fun createEventTable() {
        createEventTable(
            tableName = LIVENESS_TABLE_NAME,
            hashKeyName = PARTITION_KEY,
            scanKeyName = SCAN_KEY,
            gsi1HashKeyName = INDEX_1_PARTITION_KEY,
            gsi1ScanKeyName = INDEX_1_SCAN_KEY,
        )
    }

    private fun createEventTable(
        tableName: String,
        hashKeyName: String,
        scanKeyName: String,
        gsi1HashKeyName: String,
        gsi1ScanKeyName: String,
    ): CreateTableResponse {
        val attributeDefinitions = mutableListOf(
            AttributeDefinition.builder().attributeName(hashKeyName).attributeType(ScalarAttributeType.S).build(),
            AttributeDefinition.builder().attributeName(scanKeyName).attributeType(ScalarAttributeType.N).build(),
            AttributeDefinition.builder().attributeName(gsi1HashKeyName).attributeType(ScalarAttributeType.S).build(),
            AttributeDefinition.builder().attributeName(gsi1ScanKeyName).attributeType(ScalarAttributeType.S).build(),
        )
        return createEventTable(
            tableName = tableName,
            hashKeyName = hashKeyName,
            scanKeyName = scanKeyName,
            gsi1HashKeyName = gsi1HashKeyName,
            gsi1ScanKeyName = gsi1ScanKeyName,
            attributeDefinitions = attributeDefinitions,
        )
    }

    private fun createEventTable(
        tableName: String,
        hashKeyName: String,
        scanKeyName: String,
        gsi1HashKeyName: String,
        gsi1ScanKeyName: String,
        attributeDefinitions: List<AttributeDefinition>,
    ): CreateTableResponse {
        try {
            dynamoDbClient.deleteTable(DeleteTableRequest.builder().tableName(tableName).build())
        } catch (e: ResourceNotFoundException) {
        }
        val ks = listOf(
            KeySchemaElement.builder().attributeName(hashKeyName).keyType(KeyType.HASH).build(),
            KeySchemaElement.builder().attributeName(scanKeyName).keyType(KeyType.RANGE).build(),
        )
        val globalSecondaryIndexes: MutableList<GlobalSecondaryIndex> = ArrayList()
        addIndex(globalSecondaryIndexes, GlobalSecondaryIndexes.GSIndex1, gsi1HashKeyName, gsi1ScanKeyName)
        val provisionedThroughput =
            ProvisionedThroughput.builder().readCapacityUnits(1000L).writeCapacityUnits(1000L).build()
        val request = CreateTableRequest.builder()
            .tableName(tableName)
            .attributeDefinitions(attributeDefinitions)
            .keySchema(ks)
            .globalSecondaryIndexes(globalSecondaryIndexes)
            .provisionedThroughput(provisionedThroughput).build()

        val result = dynamoDbClient.createTable(request)

        val describeTableRequest = DescribeTableRequest.builder().tableName(tableName).build()
        val describeTableResponse = dynamoDbClient.describeTable(describeTableRequest)

        while (describeTableResponse.table().tableStatus() != TableStatus.ACTIVE) {
            // wait til table is active or otherwise will not find table
        }
        return result
    }

    private fun addIndex(
        globalSecondaryIndexes: MutableList<GlobalSecondaryIndex>,
        index: GlobalSecondaryIndexes,
        partitionKeyName: String?,
        scanKeyName: String?,
    ) {
        if (partitionKeyName != null) {
            val gsi = listOf(
                KeySchemaElement.builder().attributeName(partitionKeyName).keyType(KeyType.HASH).build(),
                KeySchemaElement.builder().attributeName(scanKeyName).keyType(KeyType.RANGE).build(),
            )
            globalSecondaryIndexes.add(
                GlobalSecondaryIndex.builder()
                    .keySchema(gsi)
                    .indexName(index.name)
                    .projection(Projection.builder().projectionType(ProjectionType.ALL).build())
                    .provisionedThroughput(
                        ProvisionedThroughput.builder().readCapacityUnits(1000L).writeCapacityUnits(1000L).build(),
                    ).build(),
            )
        }
    }
}