package ai.friday.liveness

import ai.friday.liveness.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.liveness.adapters.api.DocumentScanRequestTO
import ai.friday.liveness.adapters.dynamo.documentscan.DocumentScanEventDynamoDbRepository
import ai.friday.liveness.adapters.s3.S3LinkGenerator
import ai.friday.liveness.app.documentscan.CompletedDocumentScan
import ai.friday.liveness.app.documentscan.DigitalSpoofStatus
import ai.friday.liveness.app.documentscan.DocumentScanAdapter
import ai.friday.liveness.app.documentscan.DocumentScanCompleted
import ai.friday.liveness.app.documentscan.DocumentScanCreated
import ai.friday.liveness.app.documentscan.DocumentScanEventRepository
import ai.friday.liveness.app.documentscan.DocumentScanId
import ai.friday.liveness.app.documentscan.DocumentScanResult
import ai.friday.liveness.app.documentscan.DocumentScanSecurityCheck
import ai.friday.liveness.app.documentscan.DocumentScanStatus
import ai.friday.liveness.app.documentscan.FaceOnDocumentStatus
import ai.friday.liveness.app.documentscan.MrzStatus
import ai.friday.liveness.app.documentscan.NextStep
import ai.friday.liveness.app.documentscan.ObjectLinkGenerator
import ai.friday.liveness.app.documentscan.PendingDocumentScan
import ai.friday.liveness.app.documentscan.PendingSessionCreation
import ai.friday.liveness.app.documentscan.ProcessedDocumentScan
import ai.friday.liveness.app.documentscan.TextOnDocumentStatus
import ai.friday.liveness.app.documentscan.digestDocumentScan
import ai.friday.liveness.app.liveness.ExternalId
import ai.friday.liveness.app.session.OperationId
import ai.friday.liveness.app.session.SessionAdapter
import ai.friday.liveness.app.session.SessionTokenResult
import ai.friday.liveness.dynamo.DynamoDBUtils
import arrow.core.right
import io.kotest.core.spec.style.DescribeSpec
import io.kotest.matchers.shouldBe
import io.kotest.matchers.types.shouldBeInstanceOf
import io.micronaut.core.type.Argument
import io.micronaut.http.HttpRequest
import io.micronaut.http.HttpResponse
import io.micronaut.http.HttpStatus
import io.micronaut.http.client.HttpClient
import io.micronaut.http.client.exceptions.HttpClientResponseException
import io.micronaut.runtime.server.EmbeddedServer
import io.micronaut.test.annotation.MockBean
import io.micronaut.test.extensions.kotest.annotation.MicronautTest
import io.mockk.coEvery
import io.mockk.mockk
import java.util.Base64
import kotlinx.coroutines.reactive.awaitFirst
import org.junit.jupiter.api.assertThrows
import software.amazon.awssdk.services.s3.S3Client

@MicronautTest
class DocumentScanControllerIntegrationTest(
    embeddedServer: EmbeddedServer,
) : DescribeSpec() {
    internal val client: HttpClient =
        embeddedServer.applicationContext.createBean(HttpClient::class.java, embeddedServer.url)
    private val setupDynamoDB = DynamoDBUtils.setupDynamoDB()
    private val documentScanEventDbRepository = DocumentScanEventDynamoDbRepository(setupDynamoDB)
    private val sessionAdapterMock: SessionAdapter = mockk()
    private val documentScanAdapterMock: DocumentScanAdapter = mockk()

    @MockBean(S3LinkGenerator::class)
    fun getS3Presigner(): ObjectLinkGenerator = mockk()

    @MockBean(S3Client::class)
    fun getAwsS3Client(): S3Client = mockk(relaxed = true)

    @MockBean(SessionAdapter::class)
    fun getSessionAdapter(): SessionAdapter = sessionAdapterMock

    @MockBean(DocumentScanAdapter::class)
    fun getDocumentScanAdapter(): DocumentScanAdapter = documentScanAdapterMock

    @MockBean(DocumentScanEventDynamoDbRepository::class)
    fun getDocumentScanEventDynamoDbRepository(): DocumentScanEventRepository = documentScanEventDbRepository

    private val completeDocumentResponse = DocumentScanResult.Success(
        scannedIDPhotoFaceFoundWithMinimumQuality = false,
        didCompleteIDScanWithoutMatchingOCRTemplate = false,
        faceOnDocument = FaceOnDocumentStatus.LIKELY_ORIGINAL_FACE,
        textOnDocument = TextOnDocumentStatus.CANNOT_CONFIRM_ID_IS_AUTHENTIC,
        mrzStatus = MrzStatus.SUCCESS,
        digitalSpoofStatus = DigitalSpoofStatus.LIKELY_PHYSICAL_ID,
        documentData = "",
        sessionExtraFlags = "",
        securityCheck = DocumentScanSecurityCheck(
            documentScanCheck = true,
            auditTrailVerificationCheck = true,
            sessionTokenCheck = true,
        ),
        isDone = true,
        nextStep = NextStep.COMPLETE,
        blob = "",
    )

    init {
        beforeEach {
            DynamoDBUtils.cleanDynamoDB()
            coEvery { sessionAdapterMock.createSession(any()) } returns SessionTokenResult(token = "session-token").right()
        }

        describe("quando o operation id não existe") {
            val unexistentOperationId = OperationId(DocumentScanId.generate().value)

            describe("quando for criar uma sessão") {
                it("deve retornar unauthorized") {
                    assertThrows<HttpClientResponseException> {
                        client.createSession(unexistentOperationId)
                    }.status shouldBe HttpStatus.UNAUTHORIZED
                }

                describe("quando for realizar o scan") {
                    it("deve retornar unauthorized") {
                        assertThrows<HttpClientResponseException> {
                            client.scanDocument(DocumentScanId(unexistentOperationId.value))
                        }.status shouldBe HttpStatus.UNAUTHORIZED
                    }
                }
            }
        }

        describe("quando o operation id existe") {
            val documentScanId = DocumentScanId.generate()
            beforeEach {
                documentScanEventDbRepository.save(
                    DocumentScanCreated(
                        id = documentScanId,
                        created = getZonedDateTime(),
                        clientId = livenessClientIdFridayMain,
                        externalId = ExternalId("externalId"),
                    ),
                )
            }

            describe("quando for realizar um escaneamento de documento") {
                describe("e não tiver criado a sessão") {
                    val response = assertThrows<HttpClientResponseException> {
                        client.scanDocument(documentScanId)
                    }

                    it("deve retornar unauthorized") {
                        response.status shouldBe HttpStatus.UNAUTHORIZED
                    }

                    it("deve continuar como pendente de criação de sessão") {
                        with(documentScanEventDbRepository.findByIdOrNull(documentScanId)) {
                            shouldBeInstanceOf<PendingSessionCreation>()
                            status shouldBe DocumentScanStatus.CREATED
                        }
                    }
                }
            }

            describe("quando for criar uma sessão") {
                lateinit var sessionResponse: HttpResponse<*>
                beforeEach {
                    sessionResponse = client.createSession(OperationId(documentScanId.value))
                }

                it("deve retornar ok") {
                    sessionResponse.status shouldBe HttpStatus.OK
                }

                it("deve ficar pendente de escaneamento de documento") {
                    with(documentScanEventDbRepository.findByIdOrNull(documentScanId)) {
                        shouldBeInstanceOf<PendingDocumentScan>()
                        status shouldBe DocumentScanStatus.CREATED
                    }
                }

                describe("quando o hash do idScan existir no banco(replay)") {
                    beforeEach {
                        documentScanEventDbRepository.save(
                            DocumentScanCompleted(
                                id = DocumentScanId.generate(),
                                created = getZonedDateTime(),
                                idScan = "idScan",
                                idScanDigest = digestDocumentScan("idScan"),
                                frontImage = "some",
                                backImage = "some",
                                scannedIDPhotoFaceFoundWithMinimumQuality = false,
                                didCompleteIDScanWithoutMatchingOCRTemplate = false,
                                faceOnDocument = FaceOnDocumentStatus.LIKELY_ORIGINAL_FACE,
                                textOnDocument = TextOnDocumentStatus.CANNOT_CONFIRM_ID_IS_AUTHENTIC,
                                mrzStatus = MrzStatus.SUCCESS,
                                digitalSpoofStatus = DigitalSpoofStatus.LIKELY_PHYSICAL_ID,
                                documentData = "",
                                sessionExtraFlags = "",
                                securityCheck = DocumentScanSecurityCheck(
                                    documentScanCheck = false,
                                    auditTrailVerificationCheck = false,
                                    sessionTokenCheck = false,
                                ),
                                isDone = false,
                                nextStep = NextStep.COMPLETE,
                                blob = "blob",
                                documentsRegion = "documentsRegion",
                                documentsBucket = "documentsBucket",
                            ),
                        )
                    }

                    it("deve retornar unauthorized") {
                        assertThrows<HttpClientResponseException> {
                            client.scanDocument(documentScanId)
                        }.status shouldBe HttpStatus.UNAUTHORIZED
                    }
                }

                describe("quando o documento estiver completo") {
                    val bytes = ByteArray(1024 * 1024 * 5)

                    beforeEach {
                        coEvery { documentScanAdapterMock.scan(any()) } returns completeDocumentResponse.right()
                    }

                    describe("mesmo que seja maior que 5mb") {
                        it("deve retornar ok") {
                            client.scanDocument(documentScanId, bytes).status shouldBe HttpStatus.OK
                        }

                        it("deve mudar o estado do documento para completo") {
                            client.scanDocument(documentScanId, bytes)
                            with(documentScanEventDbRepository.findByIdOrNull(documentScanId)) {
                                shouldBeInstanceOf<CompletedDocumentScan>()
                                status shouldBe DocumentScanStatus.PROCESSED
                            }
                        }
                    }

                    it("deve retornar ok") {
                        client.scanDocument(documentScanId).status shouldBe HttpStatus.OK
                    }

                    it("deve mudar o estado do documento para completo") {
                        client.scanDocument(documentScanId)
                        with(documentScanEventDbRepository.findByIdOrNull(documentScanId)) {
                            shouldBeInstanceOf<CompletedDocumentScan>()
                            status shouldBe DocumentScanStatus.PROCESSED
                        }
                    }
                }

                describe("quando o documento nao estiver completo") {
                    beforeEach {
                        coEvery { documentScanAdapterMock.scan(any()) } returns completeDocumentResponse.copy(nextStep = NextStep.BACK).right()
                    }

                    it("deve retornar ok") {
                        val scanResponse = client.scanDocument(documentScanId)
                        scanResponse.status shouldBe HttpStatus.OK
                    }

                    it("deve mudar o estado do documento para o proximo passo") {
                        client.scanDocument(documentScanId)
                        with(documentScanEventDbRepository.findByIdOrNull(documentScanId)) {
                            shouldBeInstanceOf<ProcessedDocumentScan>()
                            status shouldBe DocumentScanStatus.CREATED
                        }
                    }

                    describe("quando processar novamente o documento") {
                        beforeEach {
                            client.scanDocument(documentScanId)
                            coEvery { documentScanAdapterMock.scan(any()) } returns completeDocumentResponse.right()
                        }

                        it("deve retornar ok") {
                            val response = client.scanDocument(documentScanId, ByteWrapper("idScan2").bytes)
                            response.status shouldBe HttpStatus.OK
                        }

                        it("deve mudar o estado do documento para completo") {
                            client.scanDocument(documentScanId, ByteWrapper("idScan2").bytes)
                            with(documentScanEventDbRepository.findByIdOrNull(documentScanId)) {
                                shouldBeInstanceOf<CompletedDocumentScan>()
                                status shouldBe DocumentScanStatus.PROCESSED
                            }
                        }
                    }
                }
            }

            describe("ao usar um operation id já completo") {
                beforeEach {
                    client.createSession(OperationId(documentScanId.value))
                    documentScanEventDbRepository.save(
                        DocumentScanCompleted(
                            id = documentScanId,
                            created = getZonedDateTime(),
                            idScan = "idScan",
                            idScanDigest = "idScanDigest",
                            frontImage = "some",
                            backImage = "some",
                            scannedIDPhotoFaceFoundWithMinimumQuality = false,
                            didCompleteIDScanWithoutMatchingOCRTemplate = false,
                            faceOnDocument = FaceOnDocumentStatus.LIKELY_ORIGINAL_FACE,
                            textOnDocument = TextOnDocumentStatus.CANNOT_CONFIRM_ID_IS_AUTHENTIC,
                            mrzStatus = MrzStatus.SUCCESS,
                            digitalSpoofStatus = DigitalSpoofStatus.LIKELY_PHYSICAL_ID,
                            documentData = "",
                            sessionExtraFlags = "",
                            securityCheck = DocumentScanSecurityCheck(
                                documentScanCheck = false,
                                auditTrailVerificationCheck = false,
                                sessionTokenCheck = false,
                            ),
                            isDone = false,
                            nextStep = NextStep.COMPLETE,
                            blob = "blob",
                            documentsRegion = "documentsRegion",
                            documentsBucket = "documentsBucket",
                        ),
                    )
                }
                describe("quando for criar uma sessão") {
                    it("deve retornar unauthorized") {
                        assertThrows<HttpClientResponseException> { client.createSession(OperationId(documentScanId.value)) }
                            .status shouldBe HttpStatus.UNAUTHORIZED
                    }

                    describe("quando for realizar um escaneamento de documento") {
                        it("deve retornar unauthorized") {
                            assertThrows<HttpClientResponseException> { client.scanDocument(documentScanId) }
                                .status shouldBe HttpStatus.UNAUTHORIZED
                        }
                    }
                }
            }
        }
    }
}

private suspend fun HttpClient.scanDocument(documentScanId: DocumentScanId, idScan: ByteArray = ByteWrapper("idScan").bytes): HttpResponse<*> {
    fun buildRequest(documentScanId: DocumentScanId) =
        HttpRequest.POST(
            "/document-scan",
            DocumentScanRequestTO(
                operationId = documentScanId.value,
                idScan = Base64.getEncoder().encodeToString(idScan),
                frontImage = "NT2NmiLi72wrTDUpViZKTQ==",
                backImage = "3UaCVtLi6bD4WapyUe/+SQ==",
            ),
        )
            .headers(mapOf("X-Device-Key" to "key", "X-User-Agent" to "agent"))

    return this.exchange(buildRequest(documentScanId), Argument.STRING, Argument.STRING).awaitFirst()
}

private suspend fun HttpClient.createSession(operationId: OperationId): HttpResponse<*> {
    fun buildRequest(operationId: OperationId) =
        HttpRequest.POST("/session", """{"operationId":"${operationId.value}","type":"DocumentScan"}""")
            .headers(
                mapOf(
                    "X-API-VERSION" to "2",
                    "X-Device-Key" to "key",
                    "X-User-Agent" to "agent",
                ),
            )

    return this.exchange(buildRequest(operationId)).awaitFirst()
}