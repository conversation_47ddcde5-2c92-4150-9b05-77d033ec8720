package ai.friday.liveness

import ai.friday.liveness.adapters.api.ConfigTO
import ai.friday.liveness.app.integrations.MessagePublisher
import ai.friday.liveness.app.liveness.LivenessService
import io.kotest.core.spec.style.DescribeSpec
import io.kotest.matchers.shouldBe
import io.micronaut.context.annotation.Property
import io.micronaut.context.annotation.PropertySource
import io.micronaut.http.HttpRequest
import io.micronaut.http.HttpResponse
import io.micronaut.http.HttpStatus
import io.micronaut.http.client.HttpClient
import io.micronaut.runtime.server.EmbeddedServer
import io.micronaut.test.annotation.MockBean
import io.micronaut.test.extensions.kotest.annotation.MicronautTest
import io.mockk.mockk
import kotlinx.coroutines.reactive.awaitFirst

@MicronautTest
@PropertySource(
    value = [
        Property(name = "integrations.facetec.client.productionKeyText", value = "pkt"),
        Property(name = "integrations.facetec.client.deviceKeyIdentifier", value = "dki"),
        Property(name = "integrations.facetec.client.publicFaceScanEncryptionKey", value = "pfsek"),
        Property(name = "integrations.facetec.host", value = "https://api.facetec.com"),
    ],
)
internal class ConfigurationControllerIntegrationTest(embeddedServer: EmbeddedServer) : DescribeSpec() {
    @MockBean(LivenessService::class)
    fun getLivenessService(): LivenessService = mockk()

    @MockBean(MessagePublisher::class)
    fun getMessagePublisher(): MessagePublisher = messagePublisher
    private val messagePublisher: MessagePublisher = mockk(relaxUnitFun = true)

    private val client: HttpClient =
        embeddedServer.applicationContext.createBean(HttpClient::class.java, embeddedServer.url)

    init {
        describe("ao solicitar a configuração de maneira anônima") {
            it("deve retornar status 200") {
                val response = client.getConfig()

                response.status shouldBe HttpStatus.OK
            }

            it("deve conter a configuração no formato correto") {
                val config = client.getConfig().body()

                config shouldBe ConfigTO(
                    deviceKeyIdentifier = "dki",
                    productionKeyText = "pkt",
                    publicFaceScanEncryptionKey = "pfsek",
                )
            }
        }
    }
}

suspend fun HttpClient.getConfig(): HttpResponse<ConfigTO> {
    return this.exchange(
        HttpRequest.GET<ConfigTO>("/config"),
        ConfigTO::class.java,
    ).awaitFirst()
}