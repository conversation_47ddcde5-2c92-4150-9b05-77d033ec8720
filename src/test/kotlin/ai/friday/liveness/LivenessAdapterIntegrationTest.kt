package ai.friday.liveness

import ai.friday.liveness.app.liveness.LivenessAdapter
import ai.friday.liveness.app.liveness.LivenessCheckRequest
import ai.friday.liveness.app.liveness.LivenessId
import io.kotest.core.annotation.Ignored
import io.kotest.core.spec.style.AnnotationSpec
import io.kotest.matchers.shouldBe
import io.micronaut.test.extensions.kotest.annotation.MicronautTest
import kotlinx.coroutines.runBlocking

@Suppress("NonAsciiCharacters")
@MicronautTest
@Ignored
internal class LivenessAdapterIntegrationTest(private val livenessAdapter: LivenessAdapter) : AnnotationSpec() {
    @Test
    fun `validacao de face scan`() {
        val result = runBlocking {
            livenessAdapter.checkLiveness(
                LivenessCheckRequest(
                    livenessId = LivenessId.generate(),
                    faceScan = "123",
                    auditTrailImage = "123",
                    lowQualityAuditTrailImage = "123",
                    deviceKeyIdentifier = "d8Shpkngoz7TfrJRPJJyfeShvgd0bsCU",
                    userAgent = "foo",
                    clientIpAddress = "************",
                ),
            )
        }

        result.isRight() shouldBe true
    }
}