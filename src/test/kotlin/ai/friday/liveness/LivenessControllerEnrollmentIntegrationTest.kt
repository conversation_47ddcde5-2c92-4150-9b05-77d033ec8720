package ai.friday.liveness

import ai.friday.liveness.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.liveness.app.liveness.EnrollmentCreated
import ai.friday.liveness.app.liveness.ExternalId
import ai.friday.liveness.app.liveness.LivenessCheckResult
import ai.friday.liveness.app.liveness.LivenessId
import arrow.core.right
import io.micronaut.runtime.server.EmbeddedServer
import io.micronaut.test.extensions.kotest.annotation.MicronautTest
import io.mockk.coEvery

@MicronautTest
internal class LivenessControllerEnrollmentIntegrationTest(
    embeddedServer: EmbeddedServer,
) : LivenessControllerIntegrationTest(
    embeddedServer,
    EnrollmentCreated(
        livenessId = LivenessId.generate(),
        created = getZonedDateTime(),
        clientId = livenessClientIdFridayMain,
        externalId = ExternalId("ACCOUNT-1"),
    ),
) {
    override suspend fun prepareDatabaseToMakeInitialValidationPass(faceMap: String?) {
    }

    override fun prepareLivenessAdapterMock(result: LivenessCheckResult) {
        coEvery {
            livenessAdapterMock.checkLiveness(any())
        } returns result.right()
    }
}