package ai.friday.liveness

import ai.friday.liveness.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.liveness.adapters.api.DocumentScanDataResponseTO
import ai.friday.liveness.adapters.api.DocumentScanImageResponseTO
import ai.friday.liveness.adapters.dynamo.documentscan.DocumentScanEventDynamoDbRepository
import ai.friday.liveness.adapters.s3.S3LinkGenerator
import ai.friday.liveness.app.documentscan.DigitalSpoofStatus
import ai.friday.liveness.app.documentscan.DocumentScanAdapter
import ai.friday.liveness.app.documentscan.DocumentScanCompleted
import ai.friday.liveness.app.documentscan.DocumentScanCreated
import ai.friday.liveness.app.documentscan.DocumentScanEventRepository
import ai.friday.liveness.app.documentscan.DocumentScanId
import ai.friday.liveness.app.documentscan.DocumentScanSecurityCheck
import ai.friday.liveness.app.documentscan.DocumentScanSessionCreated
import ai.friday.liveness.app.documentscan.FaceOnDocumentStatus
import ai.friday.liveness.app.documentscan.MrzStatus
import ai.friday.liveness.app.documentscan.NextStep
import ai.friday.liveness.app.documentscan.ObjectLinkGenerator
import ai.friday.liveness.app.documentscan.TextOnDocumentStatus
import ai.friday.liveness.app.documentscan.digestDocumentScan
import ai.friday.liveness.app.liveness.ExternalId
import ai.friday.liveness.dynamo.DynamoDBUtils
import io.kotest.core.spec.style.DescribeSpec
import io.kotest.matchers.shouldBe
import io.micronaut.core.type.Argument
import io.micronaut.http.HttpRequest
import io.micronaut.http.HttpStatus
import io.micronaut.http.client.HttpClient
import io.micronaut.http.client.exceptions.HttpClientResponseException
import io.micronaut.runtime.server.EmbeddedServer
import io.micronaut.test.annotation.MockBean
import io.micronaut.test.extensions.kotest.annotation.MicronautTest
import io.mockk.coEvery
import io.mockk.every
import io.mockk.mockk
import org.junit.jupiter.api.assertThrows
import software.amazon.awssdk.services.s3.S3Client
import software.amazon.awssdk.services.s3.model.GetObjectRequest

private const val username = "LIVENESS_CLIENT_ID-aaaaaaaa-bbbb-cccc-dddd-eeeeeeeeeeee"
private const val password = "senha-correta"

@MicronautTest
class InternalDocumentScanControllerIntegrationTest(embeddedServer: EmbeddedServer) : DescribeSpec() {
    internal val client: HttpClient =
        embeddedServer.applicationContext.createBean(HttpClient::class.java, embeddedServer.url)
    private val setupDynamoDB = DynamoDBUtils.setupDynamoDB()
    private val documentScanEventDbRepositoryMock = DocumentScanEventDynamoDbRepository(setupDynamoDB)
    private val documentScanAdapterMock: DocumentScanAdapter = mockk()
    private val s3ClientMock = mockk<S3Client>(relaxed = true)
    private val s3PresignerMock: ObjectLinkGenerator = mockk(relaxed = true) {
        coEvery { getObjectPresigned(any(), any()) } returns "object://url"
    }

    @MockBean(S3LinkGenerator::class)
    fun getS3Presigner(): ObjectLinkGenerator = s3PresignerMock

    @MockBean(S3Client::class)
    fun getAwsS3Client(): S3Client = s3ClientMock

    @MockBean(DocumentScanAdapter::class)
    fun getDocumentScanAdapter(): DocumentScanAdapter = documentScanAdapterMock

    @MockBean(DocumentScanEventDynamoDbRepository::class)
    fun getDocumentScanEventDynamoDbRepository(): DocumentScanEventRepository = documentScanEventDbRepositoryMock

    init {
        beforeEach {
            DynamoDBUtils.cleanDynamoDB()
        }

        describe("quando as credenciais são inválidas") {
            val exception = assertThrows<HttpClientResponseException> {
                client.toBlocking().retrieve(buildPostCreateDocumentScan())
            }

            it("deve retornar unauthorized") {
                exception.status shouldBe HttpStatus.UNAUTHORIZED
            }
        }

        describe("quando as informações estão corretas") {
            it("deve processar normalmente a chamada") {
                val result = client.toBlocking().exchange(buildPostCreateDocumentScan().basicAuth(username, password), Argument.STRING, Argument.STRING)

                result.status shouldBe HttpStatus.CREATED
            }
        }

        lateinit var documentScanId: DocumentScanId

        beforeEach { documentScanId = DocumentScanId.generate() }

        describe("quando não existe um document scan") {
            it("deve retornar conflict ao chamar a consulta de dados") {
                assertThrows<HttpClientResponseException> { client.toBlocking().retrieve(buildGetDocumentScanData(documentScanId)) }
                    .status shouldBe HttpStatus.NOT_FOUND
            }

            it("deve retornar conflict ao chamar a consulta de imagens") {
                assertThrows<HttpClientResponseException> { client.toBlocking().retrieve(buildGetDocumentScanImages(documentScanId)) }
                    .status shouldBe HttpStatus.NOT_FOUND
            }
        }

        describe("quando um document scan está incompleto") {
            beforeEach {
                documentScanEventDbRepositoryMock.save(
                    DocumentScanCreated(
                        id = documentScanId,
                        created = getZonedDateTime(),
                        clientId = livenessClientIdFridayMain,
                        externalId = ExternalId("ACCOUNT-1"),
                    ),
                )
            }

            it("deve retornar conflict ao chamar a consulta de imagens") {
                assertThrows<HttpClientResponseException> { client.toBlocking().retrieve(buildGetDocumentScanImages(documentScanId)) }
                    .status shouldBe HttpStatus.CONFLICT
            }

            it("deve retornar conflict ao chamar a consulta de dados") {
                assertThrows<HttpClientResponseException> { client.toBlocking().retrieve(buildGetDocumentScanData(documentScanId)) }
                    .status shouldBe HttpStatus.CONFLICT
            }
        }

        describe("quando um document scan está completo") {
            beforeEach {
                documentScanEventDbRepositoryMock.save(
                    DocumentScanCreated(
                        id = documentScanId,
                        created = getZonedDateTime(),
                        clientId = livenessClientIdFridayMain,
                        externalId = ExternalId("ACCOUNT-1"),
                    ),
                )

                documentScanEventDbRepositoryMock.save(
                    DocumentScanSessionCreated(
                        id = documentScanId,
                        created = getZonedDateTime().plusMinutes(1),
                        deviceKeyIdentifier = "",
                        userAgent = "",
                        clientIpAddress = "",
                    ),
                )

                documentScanEventDbRepositoryMock.save(
                    DocumentScanCompleted(
                        id = documentScanId,
                        created = getZonedDateTime().plusMinutes(2),
                        idScan = "idScan",
                        idScanDigest = digestDocumentScan("idScan"),
                        frontImage = "some",
                        backImage = "some",
                        scannedIDPhotoFaceFoundWithMinimumQuality = false,
                        didCompleteIDScanWithoutMatchingOCRTemplate = false,
                        faceOnDocument = FaceOnDocumentStatus.LIKELY_ORIGINAL_FACE,
                        textOnDocument = TextOnDocumentStatus.CANNOT_CONFIRM_ID_IS_AUTHENTIC,
                        mrzStatus = MrzStatus.SUCCESS,
                        digitalSpoofStatus = DigitalSpoofStatus.LIKELY_PHYSICAL_ID,
                        documentData = "",
                        sessionExtraFlags = "",
                        securityCheck = DocumentScanSecurityCheck(
                            documentScanCheck = false,
                            auditTrailVerificationCheck = false,
                            sessionTokenCheck = false,
                        ),
                        isDone = false,
                        nextStep = NextStep.COMPLETE,
                        blob = "blob",
                        documentsRegion = "documentsRegion",
                        documentsBucket = "documentsBucket",
                    ),
                )
            }

            it("deve retornar as imagens do document scan") {
                every { s3ClientMock.getObject(any<GetObjectRequest>()) } returns mockk(relaxed = true)
                val result = client.toBlocking().exchange(buildGetDocumentScanImages(documentScanId), DocumentScanImageResponseTO::class.java)

                result.status shouldBe HttpStatus.OK

                with(result.body()!!) {
                    frontImage shouldBe "object://url"
                    backImage shouldBe "object://url"
                }
            }

            it("deve retornar os dados do document scan") {
                val result = client.toBlocking().exchange(buildGetDocumentScanData(documentScanId), DocumentScanDataResponseTO::class.java)

                result.status shouldBe HttpStatus.OK

                with(result.body()!!) {
                    face shouldBe FaceOnDocumentStatus.LIKELY_ORIGINAL_FACE.name
                    ocr shouldBe true
                    text shouldBe TextOnDocumentStatus.CANNOT_CONFIRM_ID_IS_AUTHENTIC.name
                    spoof shouldBe DigitalSpoofStatus.LIKELY_PHYSICAL_ID.name
                }
            }
        }
    }
}

private fun buildGetDocumentScanImages(documentScanId: DocumentScanId) =
    HttpRequest.GET<Unit>("/internal/document-scan/${documentScanId.value}/images")
        .header("Content-Type", "application/json")
        .basicAuth(username, password)

private fun buildGetDocumentScanData(documentScanId: DocumentScanId) =
    HttpRequest.GET<Unit>("/internal/document-scan/${documentScanId.value}")
        .header("Content-Type", "application/json")
        .basicAuth(username, password)

private fun buildPostCreateDocumentScan(externalId: String = "123") =
    HttpRequest.POST("/internal/document-scan", """{"externalId":"$externalId"}""")
        .header("Content-Type", "application/json")