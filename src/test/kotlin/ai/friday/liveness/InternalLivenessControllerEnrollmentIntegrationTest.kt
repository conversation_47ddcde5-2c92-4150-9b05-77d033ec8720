package ai.friday.liveness

import ai.friday.liveness.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.liveness.adapters.api.CheckEnrollmentResponseTO
import ai.friday.liveness.adapters.api.CreateLivenessResponseTO
import ai.friday.liveness.adapters.api.CreateLivenessTO
import ai.friday.liveness.adapters.dynamo.LivenessEventDynamoDbRepository
import ai.friday.liveness.adapters.facetech.FaceTecSDKConfig
import ai.friday.liveness.adapters.micronaut.FeaturesConfigurationMicronaut
import ai.friday.liveness.app.LivenessClientId
import ai.friday.liveness.app.integrations.MessagePublisher
import ai.friday.liveness.app.liveness.AgeEstimateGroup
import ai.friday.liveness.app.liveness.EnrollmentCreated
import ai.friday.liveness.app.liveness.ExternalId
import ai.friday.liveness.app.liveness.FaceMapSearchAdapter
import ai.friday.liveness.app.liveness.FaceScanReceived
import ai.friday.liveness.app.liveness.FaceVerified
import ai.friday.liveness.app.liveness.FeatureConfiguration
import ai.friday.liveness.app.liveness.LivenessAdapter
import ai.friday.liveness.app.liveness.LivenessCheckResult
import ai.friday.liveness.app.liveness.LivenessEventRepository
import ai.friday.liveness.app.liveness.LivenessId
import ai.friday.liveness.app.liveness.LivenessSecurityCheck
import ai.friday.liveness.app.liveness.LivenessStatus
import ai.friday.liveness.app.liveness.LivenessType
import ai.friday.liveness.app.liveness.PendingSessionCreation
import ai.friday.liveness.app.liveness.PlatformType
import ai.friday.liveness.app.liveness.SessionCreated
import ai.friday.liveness.app.session.SessionAdapter
import ai.friday.liveness.app.session.SessionTokenResult
import ai.friday.liveness.dynamo.DynamoDBUtils.cleanDynamoDB
import ai.friday.liveness.dynamo.DynamoDBUtils.setupDynamoDB
import arrow.core.right
import io.kotest.core.spec.style.DescribeSpec
import io.kotest.matchers.nulls.shouldNotBeNull
import io.kotest.matchers.optional.shouldBePresent
import io.kotest.matchers.shouldBe
import io.kotest.matchers.string.shouldNotBeEmpty
import io.kotest.matchers.types.shouldBeInstanceOf
import io.micronaut.core.type.Argument
import io.micronaut.http.HttpRequest
import io.micronaut.http.HttpResponse
import io.micronaut.http.HttpStatus
import io.micronaut.http.client.HttpClient
import io.micronaut.http.client.exceptions.HttpClientResponseException
import io.micronaut.runtime.server.EmbeddedServer
import io.micronaut.test.annotation.MockBean
import io.micronaut.test.extensions.kotest.annotation.MicronautTest
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.reactive.awaitFirst
import org.junit.jupiter.api.assertThrows

@MicronautTest
internal class InternalLivenessControllerEnrollmentIntegrationTest(private val server: EmbeddedServer) :
    DescribeSpec(
        {
            val livenessRepository = server.applicationContext.getBean(LivenessEventRepository::class.java)
            val client = server.applicationContext.createBean(HttpClient::class.java, server.url)

            describe("ao pedir para criar um enrollment sem as credenciais") {
                val exception = assertThrows<HttpClientResponseException> {
                    client.toBlocking().retrieve(buildPostEnrollment())
                }

                it("deve retornar unauthorized") {
                    exception.status shouldBe HttpStatus.UNAUTHORIZED
                }
            }

            describe("ao pedir para criar um enrollment com as credenciais") {
                val username = LivenessClientId("LIVENESS_CLIENT_ID-aaaaaaaa-bbbb-cccc-dddd-eeeeeeeeeeee").value

                describe("e as credenciais são inválidas") {
                    val password = "senha-errada"

                    val exception = assertThrows<HttpClientResponseException> {
                        client.retrieve(buildPostEnrollment().basicAuth(username, password)).awaitFirst()
                    }

                    it("deve retornar unauthorized") {
                        exception.status shouldBe HttpStatus.UNAUTHORIZED
                    }
                }

                describe("e as credenciais são válidas") {
                    val password = "senha-correta"

                    beforeContainer { cleanDynamoDB() }

                    describe("se o external id for vazio") {
                        val thrown = assertThrows<HttpClientResponseException> {
                            client.exchange(
                                buildPostEnrollment("").basicAuth(username, password),
                                Argument.STRING,
                            ).awaitFirst()
                        }

                        it("deve retornar bad request") {
                            thrown.status shouldBe HttpStatus.BAD_REQUEST
                        }
                    }

                    describe("se o external id for válido") {
                        var response: HttpResponse<String>? = null
                        beforeTest {
                            response = client
                                .exchange(
                                    buildPostEnrollment("123").basicAuth(username, password),
                                    Argument.STRING,
                                ).awaitFirst()
                        }

                        it("deve retornar created") {
                            response!!.status shouldBe HttpStatus.CREATED
                        }

                        it("deve retornar o livenessId") {
                            response!!.getBody(CreateLivenessResponseTO::class.java).shouldBePresent {
                                it.livenessId.shouldNotBeEmpty()
                            }
                        }

                        it("deve persistir o liveness no banco corretamente") {

                            val liveness = livenessRepository.findByIdOrNull(
                                LivenessId(response!!.getBody(CreateLivenessResponseTO::class.java).get().livenessId),
                            )

                            liveness.shouldNotBeNull()
                            liveness.status shouldBe LivenessStatus.CREATED
                            liveness.type shouldBe LivenessType.ENROLLMENT
                            liveness.shouldBeInstanceOf<PendingSessionCreation>()
                        }

                        it("deve retornar que o external id não possui um enrollment concluido") {
                            val checkResponse = client
                                .exchange(
                                    buildCheckEnrollment("123").basicAuth(username, password),
                                    Argument.of(CheckEnrollmentResponseTO::class.java),
                                ).awaitFirst()

                            checkResponse.status shouldBe HttpStatus.OK
                            checkResponse.body()!!.hasCompletedLiveness shouldBe false
                        }
                    }

                    describe("se o external id já tiver iniciado outro enrollment") {
                        val livenessId = LivenessId.generate()
                        beforeTest {
                            cleanDynamoDB()
                            livenessRepository.save(
                                EnrollmentCreated(
                                    livenessId = livenessId,
                                    created = getZonedDateTime(),
                                    clientId = LivenessClientId(value = username),
                                    externalId = ExternalId(value = "123"),
                                ),
                            )
                            livenessRepository.save(
                                SessionCreated(
                                    livenessId = livenessId,
                                    created = getZonedDateTime(),
                                    deviceKeyIdentifier = "deviceKeyIdentifier",
                                    userAgent = "userAgent",
                                    clientIpAddress = "***********",
                                ),
                            )
                        }

                        it("deve retornar o mesmo livenessId") {
                            val response =

                                client
                                    .exchange(
                                        buildPostEnrollment("123").basicAuth(username, password),
                                        Argument.STRING,
                                    ).awaitFirst()
                            response.status shouldBe HttpStatus.CREATED
                            response.getBody(CreateLivenessResponseTO::class.java)
                                .get().livenessId shouldBe livenessId.value
                        }

                        describe("se o external id já tiver concluido outro enrollment") {
                            beforeTest {
                                livenessRepository.save(
                                    FaceScanReceived(
                                        livenessId = livenessId,
                                        created = getZonedDateTime(),
                                        faceScanDigest = "faceScanDigest",
                                    ),
                                )
                                livenessRepository.save(
                                    FaceVerified(
                                        livenessId = livenessId,
                                        created = getZonedDateTime(),
                                        success = true,
                                        ageEstimateGroup = AgeEstimateGroup.UNKNOWN,
                                        platformType = PlatformType.UNKNOWN,
                                        deviceSDKVersion = "deviceSDKVersion",
                                        scanResultBlob = "scanResultBlob",
                                        securityCheck = LivenessSecurityCheck(
                                            faceScanLivenessCheck = true,
                                            auditTrailVerificationCheck = true,
                                            sessionTokenCheck = true,
                                        ),
                                        faceMap = "RkFDRV9NQVBfRkFLRQ==",
                                        auditTrailImage = "QVVESVRfVFJBSUxfSU1BR0VfRkFLRQ==",
                                    ),
                                )
                            }

                            it("deve retornar conflict") {
                                val exception =
                                    assertThrows<HttpClientResponseException> {
                                        client
                                            .exchange(
                                                buildPostEnrollment("123").basicAuth(username, password),
                                                Argument.STRING,
                                            ).awaitFirst()
                                    }
                                exception.status shouldBe HttpStatus.CONFLICT
                            }

                            it("deve retornar que o external id possui um enrollment concluido") {
                                val checkResponse = client
                                    .exchange(
                                        buildCheckEnrollment("123").basicAuth(username, password),
                                        Argument.of(CheckEnrollmentResponseTO::class.java),
                                    ).awaitFirst()

                                checkResponse.status shouldBe HttpStatus.OK
                                checkResponse.body()!!.hasCompletedLiveness shouldBe true
                            }
                        }
                    }
                }
            }
        },
    ) {
    private val setupDynamoDB = setupDynamoDB()
    private val livenessEventDbrepository = LivenessEventDynamoDbRepository(setupDynamoDB)

    private val sessionAdapter: SessionAdapter = mockk {
        coEvery { createSession(any()) } returns SessionTokenResult(token = "123").right()
    }

    private val livenessAdapterMock: LivenessAdapter = mockk {
        coEvery { checkLiveness(any()) } returns LivenessCheckResult.createSuccessResult(
            ageEstimateGroup = AgeEstimateGroup.OVER_THIRTY,
            platformType = PlatformType.IOS,
            deviceSDKVersion = "deviceSDKVersion",
            securityCheck = LivenessSecurityCheck(
                sessionTokenCheck = true,
                auditTrailVerificationCheck = true,
                faceScanLivenessCheck = true,
            ),
            faceMap = ByteWrapper("FACE_MAP_FAKE".toByteArray()),
            scanResultBlobFactory = { "blob" },
        ).right()
    }

    private val faceMapSearchAdapterMock: FaceMapSearchAdapter = mockk()

    @MockBean(FeaturesConfigurationMicronaut::class)
    fun getFeaturesConfigurationMicronaut(): FeatureConfiguration = featureConfiguration
    private val featureConfiguration = mockk<FeatureConfiguration>(relaxed = true)

    @MockBean(FaceTecSDKConfig::class)
    fun getFaceTecSDKConfig(): FaceTecSDKConfig = FaceTecSDKConfig().apply {
        deviceKeyIdentifier = ""
        serverKey = ""
        productionKeyText = ""
        faceMapEncryptionKey = ""
        usageLogsServerUri = ""
    }

    @MockBean(SessionAdapter::class)
    fun getSessionAdapter(): SessionAdapter = sessionAdapter

    @MockBean(LivenessAdapter::class)
    fun getLivenessAdapter(): LivenessAdapter = livenessAdapterMock

    @MockBean(FaceMapSearchAdapter::class)
    fun getFaceMapSearchAdapter(): FaceMapSearchAdapter = faceMapSearchAdapterMock

    @MockBean(LivenessEventDynamoDbRepository::class)
    fun getLivenessEventRepository(): LivenessEventRepository = livenessEventDbrepository

    @MockBean(MessagePublisher::class)
    fun getMessagePublisher(): MessagePublisher = messagePublisher
    private val messagePublisher: MessagePublisher = mockk(relaxUnitFun = true)
}

private fun buildPostEnrollment(externalId: String = "123") =
    HttpRequest.POST("/internal/enrollment", CreateLivenessTO(externalId = externalId))

private fun buildCheckEnrollment(externalId: String = "123") =
    HttpRequest.GET<Unit>("/internal/enrollment/hasCompletedEnrollment/$externalId")