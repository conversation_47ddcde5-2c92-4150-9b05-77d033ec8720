package archunit

import com.tngtech.archunit.core.domain.JavaClasses
import com.tngtech.archunit.core.importer.ImportOption
import com.tngtech.archunit.junit.AnalyzeClasses
import com.tngtech.archunit.junit.ArchTest
import com.tngtech.archunit.lang.syntax.ArchRuleDefinition.noClasses

private const val NAMESPACE = "ai.friday.liveness"

@AnalyzeClasses(packages = [NAMESPACE], importOptions = [ImportOption.DoNotIncludeTests::class])
internal class HexagonalTest {

    @ArchTest
    fun testHexagonal(importedClasses: JavaClasses) {
        noClasses().that()
            .resideInAPackage("$NAMESPACE.app..")
            .should().dependOnClassesThat().resideInAPackage("$NAMESPACE.adapters..")
            .check(importedClasses)
    }
}