{"memory": "2048", "networkMode": "awsvpc", "family": "liveness-gateway-task", "placementConstraints": [], "cpu": "512", "executionRoleArn": "arn:aws:iam::402556871325:role/liveness-task-execution-role", "volumes": [{"name": "test-efs-volume", "efsVolumeConfiguration": {"fileSystemId": "fs-01421857144706bc9", "rootDirectory": "/"}}], "requiresCompatibilities": ["FARGATE"], "taskRoleArn": "arn:aws:iam::402556871325:role/liveness-task-role", "containerDefinitions": [{"environment": [{"name": "MICRONAUT_ENVIRONMENTS", "value": "staging"}, {"name": "DD_SERVICE", "value": "liveness-gateway"}, {"name": "DD_ENV", "value": "staging"}], "name": "LivenessAPI", "mountPoints": [], "secrets": [{"valueFrom": "arn:aws:secretsmanager:us-east-1:402556871325:secret:liveness-gateway/facetec-UMJ3f8:DEVICE_KEY_IDENTIFIER::", "name": "INTEGRATIONS_FACETEC_SDK_DEVICE_KEY_IDENTIFIER"}, {"valueFrom": "arn:aws:secretsmanager:us-east-1:402556871325:secret:liveness-gateway/facetec-UMJ3f8:SERVER_KEY::", "name": "INTEGRATIONS_FACETEC_SDK_SERVER_KEY"}, {"valueFrom": "arn:aws:secretsmanager:us-east-1:402556871325:secret:liveness-gateway/facetec-UMJ3f8:PRODUCTION_KEY_TEXT::", "name": "INTEGRATIONS_FACETEC_SDK_PRODUCTION_KEY_TEXT"}], "image": "x", "dockerLabels": {"com.datadoghq.tags.service": "liveness-gateway", "com.datadoghq.tags.env": "staging"}, "cpu": 492, "portMappings": [{"protocol": "tcp", "containerPort": 8443, "hostPort": 8443}], "logConfiguration": {"logDriver": "awsfire<PERSON>s", "options": {"auto_create_group": "false", "region": "us-east-1", "Name": "cloudwatch", "log_group_name": "/ecs/liveness-gateway-task", "log_stream_name": "ecs/liveness-gateway-task/$(ecs_task_id)"}}, "memory": 1536, "essential": true, "volumesFrom": []}, {"environment": [{"name": "DD_SITE", "value": "datadoghq.com"}, {"name": "ECS_FARGATE", "value": "true"}], "name": "datadog-agent", "mountPoints": [], "secrets": [{"valueFrom": "arn:aws:secretsmanager:us-east-1:402556871325:secret:bill-payment-api/datadog-S3QmAq:DD_API_KEY::", "name": "DD_API_KEY"}], "image": "datadog/agent:latest", "cpu": 10, "portMappings": [{"protocol": "tcp", "containerPort": 8125, "hostPort": 8125}], "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-stream-prefix": "ecs", "awslogs-group": "/ecs/liveness-gateway-dd", "awslogs-region": "us-east-1"}}, "memory": 256, "essential": true, "volumesFrom": []}, {"memoryReservation": 50, "environment": [{"name": "DD_SERVICE", "value": "liveness-gateway"}, {"name": "SERVICE_CONTAINER_NAME", "value": "LivenessAPI"}, {"name": "DD_ENV", "value": "staging"}, {"name": "LOG_GROUP_NAME", "value": "/logs/liveness-gateway-task"}], "name": "log_router", "mountPoints": [], "secrets": [{"valueFrom": "arn:aws:secretsmanager:us-east-1:402556871325:secret:bill-payment-api/datadog-S3QmAq:DD_API_KEY::", "name": "DD_API_KEY"}], "image": "402556871325.dkr.ecr.us-east-1.amazonaws.com/custom-fluent-bit:2.3", "firelensConfiguration": {"type": "fluentbit", "options": {"config-file-type": "file", "config-file-value": "/general/general.conf"}}, "cpu": 0, "portMappings": [], "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-stream-prefix": "ecs", "awslogs-group": "/ecs/liveness-gateway-dd", "awslogs-region": "us-east-1"}}, "user": "0", "essential": true, "volumesFrom": []}, {"environment": [], "name": "test-efs", "mountPoints": [{"sourceVolume": "test-efs-volume", "containerPath": "/logs"}], "image": "402556871325.dkr.ecr.us-east-1.amazonaws.com/test-efs:1.0", "cpu": 10, "portMappings": [{"protocol": "tcp", "containerPort": 3000, "hostPort": 3000}, {"protocol": "tcp", "containerPort": 2049, "hostPort": 2049}], "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-stream-prefix": "ecs", "awslogs-group": "/ecs/liveness-gateway-dd", "awslogs-region": "us-east-1"}}, "memory": 256, "essential": false, "volumesFrom": []}]}